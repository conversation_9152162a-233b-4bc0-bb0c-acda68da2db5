import React, { useState } from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  Button,
  Chip,
  Typography,
  Paper,
  Autocomplete,
  FormControlLabel,
  Checkbox,
  IconButton,
  Stack,
  ButtonGroup,
  Divider,
  Tooltip,
  ToggleButtonGroup,
  ToggleButton,
  Collapse
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearAllIcon from '@mui/icons-material/ClearAll';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import TodayIcon from '@mui/icons-material/Today';
import DateRangeIcon from '@mui/icons-material/DateRange';
import EventIcon from '@mui/icons-material/Event';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ManifestStatus } from '../../types/manifest';
import { LocationZone } from '../../types/location';
import { VehicleType } from '../../types/Vehicle';
import { addDays, startOfToday, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays } from 'date-fns';

interface FilterState {
  search: string;
  status: ManifestStatus[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  dateFieldType: 'createdDate' | 'deliveryDate' | 'deliveredDate';
  location: string;
  deliveryVehicle: string;
  hasDeliveryDate: boolean;
}

interface Totals {
  totalCBM: number;
  totalPallets: number;
  totalPieces: number;
  totalInboundPieces: number;
  byStatus: Record<ManifestStatus, number>;
}

interface ManifestListFiltersProps {
  filters: FilterState;
  onFilterChange: (field: string, value: any) => void;
  onResetFilters: () => void;
  locationZones: LocationZone[];
  vehicleTypes: VehicleType[];
  loading?: {
    locationsLoading?: boolean;
    vehicleTypesLoading?: boolean;
  };
  totals?: Totals;
}

const ManifestListFilters: React.FC<ManifestListFiltersProps> = ({
  filters,
  onFilterChange,
  onResetFilters,
  locationZones,
  vehicleTypes,
  loading = {},
  totals
}) => {
  const { locationsLoading = false, vehicleTypesLoading = false } = loading;

  // State for collapsible filters - open by default
  const [filtersExpanded, setFiltersExpanded] = useState(true);

  // Date field type options for autocomplete
  const dateFieldOptions = [
    { name: 'Created Date' },
    { name: 'Delivery Date' },
    { name: 'Delivered Date' }
  ];

  // Helper to convert display name to field type
  const getFieldTypeFromName = (name: string): 'createdDate' | 'deliveryDate' | 'deliveredDate' => {
    switch (name) {
      case 'Delivery Date': return 'deliveryDate';
      case 'Delivered Date': return 'deliveredDate';
      default: return 'createdDate';
    }
  };

  // Helper to convert field type to display name
  const getNameFromFieldType = (fieldType: string): string => {
    switch (fieldType) {
      case 'deliveryDate': return 'Delivery Date';
      case 'deliveredDate': return 'Delivered Date';
      default: return 'Created Date';
    }
  };
  
  // Helper for status filter display
  const getStatusLabel = (status: string): string => {
    return status.replace(/_/g, ' ');
  };
  
  // Get status chip colors - matching ManifestManagement implementation
  const getStatusChipColor = (status: ManifestStatus) => {
    switch (status) {
      case ManifestStatus.CREATED:
        return { bg: '#e3f2fd', color: '#1976d2' }; // Light blue
      case ManifestStatus.ETA_TO_WAREHOUSE:
        return { bg: '#fff8e1', color: '#ff8f00' }; // Amber
      case ManifestStatus.ARRIVED:
        return { bg: '#f1f8e9', color: '#689f38' }; // Light green
      case ManifestStatus.ON_HOLD:
        return { bg: '#ffebee', color: '#d32f2f' }; // Red
      case ManifestStatus.INBOUNDING:
        return { bg: '#fff3e0', color: '#ef6c00' }; // Orange
      case ManifestStatus.INBOUNDED_TO_WAREHOUSE:
        return { bg: '#e8eaf6', color: '#3f51b5' }; // Indigo
      case ManifestStatus.READY_TO_DELIVER:
        return { bg: '#e0f7fa', color: '#00838f' }; // Cyan
      case ManifestStatus.PENDING_DELIVER:
        return { bg: '#ede7f6', color: '#673ab7' }; // Deep purple
      case ManifestStatus.DELIVERING:
        return { bg: '#e8f5e9', color: '#2e7d32' }; // Green
      case ManifestStatus.DELIVERED:
        return { bg: '#e0f2f1', color: '#00695c' }; // Teal
      case ManifestStatus.DISCREPANCY:
        return { bg: '#ffcdd2', color: '#c62828' }; // Bright Red
      default:
        return { bg: '#f5f5f5', color: '#757575' }; // Gray
    }
  };
  
  // Helper for status filter selection
  const handleStatusFilterChange = (status: ManifestStatus) => {
    const currentStatuses = [...filters.status];
    const statusIndex = currentStatuses.indexOf(status);
    
    if (statusIndex === -1) {
      // Add status to filter
      onFilterChange('status', [...currentStatuses, status]);
    } else {
      // Remove status from filter
      currentStatuses.splice(statusIndex, 1);
      onFilterChange('status', currentStatuses);
    }
  };
  
  // Set predefined date ranges
  const handleDateRangePreset = (preset: string) => {
    const today = startOfToday();
    
    switch(preset) {
      case 'today':
        onFilterChange('dateRange.start', today);
        onFilterChange('dateRange.end', today);
        break;
      case 'yesterday':
        const yesterday = subDays(today, 1);
        onFilterChange('dateRange.start', yesterday);
        onFilterChange('dateRange.end', yesterday);
        break;
      case 'thisWeek':
        onFilterChange('dateRange.start', startOfWeek(today));
        onFilterChange('dateRange.end', endOfWeek(today));
        break;
      case 'thisMonth':
        onFilterChange('dateRange.start', startOfMonth(today));
        onFilterChange('dateRange.end', endOfMonth(today));
        break;
      case 'last7days':
        onFilterChange('dateRange.start', subDays(today, 6));
        onFilterChange('dateRange.end', today);
        break;
      case 'last30days':
        onFilterChange('dateRange.start', subDays(today, 29));
        onFilterChange('dateRange.end', today);
        break;
      case 'next7days':
        onFilterChange('dateRange.start', today);
        onFilterChange('dateRange.end', addDays(today, 6));
        break;
      case 'clear':
        onFilterChange('dateRange.start', null);
        onFilterChange('dateRange.end', null);
        break;
    }
  };
  
  const handleDateRangeChange = (type: 'start' | 'end', date: Date | null) => {
    onFilterChange(`dateRange.${type}`, date);
  };
  
  const handleLocationFilterChange = (event: React.SyntheticEvent, value: LocationZone | null) => {
    onFilterChange('location', value ? value.name : '');
  };
  
  const handleDeliveryVehicleFilterChange = (event: React.SyntheticEvent, value: VehicleType | null) => {
    onFilterChange('deliveryVehicle', value ? value.name : '');
  };
  
  const handleHasDeliveryDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange('hasDeliveryDate', event.target.checked);
  };
  
  const handleDateFieldTypeChange = (event: React.SyntheticEvent, value: { name: string } | null) => {
    onFilterChange('dateFieldType', value ? getFieldTypeFromName(value.name) : 'createdDate');
  };
  
  return (
    <Paper sx={{
      mb: 0,
      borderRadius: 0,
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
      overflow: 'visible',
      position: 'relative',
      zIndex: 1, // Lower than sticky header
    }}>
      {/* Collapsible header */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 1.5,
        py: 0.75,
        borderBottom: filtersExpanded ? '1px solid rgba(224, 224, 224, 0.5)' : 'none',
        cursor: 'pointer',
        minHeight: '36px',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.02)'
        }
      }} onClick={() => setFiltersExpanded(!filtersExpanded)}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
          <FilterListIcon sx={{ fontSize: '16px' }} color="primary" />
          <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main', fontSize: '0.8rem' }}>
            Filters
          </Typography>
          {/* Show active filter count */}
          {(filters.search || filters.status.length > 0 || filters.dateRange.start ||
            filters.dateRange.end || filters.location || filters.deliveryVehicle ||
            filters.hasDeliveryDate) && (
            <Chip
              size="small"
              label="Active"
              color="primary"
              sx={{ height: '16px', fontSize: '0.65rem', '& .MuiChip-label': { px: 0.5 } }}
            />
          )}
        </Box>
        <IconButton
          size="small"
          sx={{
            p: 0.25,
            width: '24px',
            height: '24px',
            transition: 'transform 0.2s ease',
            transform: filtersExpanded ? 'rotate(0deg)' : 'rotate(0deg)'
          }}
        >
          {filtersExpanded ? <ExpandLessIcon sx={{ fontSize: '16px' }} /> : <ExpandMoreIcon sx={{ fontSize: '16px' }} />}
        </IconButton>
      </Box>

      {/* Collapsible filters content */}
      <Collapse in={filtersExpanded} timeout={300}>
        <Box sx={{ p: 1.5, pt: 1 }}>
          {/* Main container for all filters with grid layout */}
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(12, 1fr)', gap: 1 }}> {/* Reduced gap from 1.5 to 1 */}
        {/* Left section: Date controls - spans 6 columns */}
        <Box sx={{ gridColumn: 'span 6', display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1 }}>
          {/* Date field type selector */}
          <Box sx={{ gridColumn: 'span 2' }}>
            <Autocomplete
              size="small"
              options={dateFieldOptions}
              getOptionLabel={(option) => option.name}
              value={dateFieldOptions.find(option => option.name === getNameFromFieldType(filters.dateFieldType || 'createdDate')) || null}
              onChange={handleDateFieldTypeChange}
              loading={false}
              slotProps={{
                popper: {
                  sx: {
                    '& .MuiAutocomplete-listbox': {
                      fontSize: '0.75rem'
                    }
                  },
                  modifiers: [
                    {
                      name: 'flip',
                      enabled: false,
                    },
                    {
                      name: 'preventOverflow',
                      enabled: true,
                      options: {
                        altAxis: true,
                        altBoundary: true,
                        tether: true,
                        rootBoundary: 'document',
                      },
                    },
                  ],
                }
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Filter by Date Field"
                   fullWidth
                    size="small"
                    variant="outlined"
                    sx={{ minHeight: '32px' }}
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        sx: { fontSize: '0.75rem'}
                      },
                      inputLabel: {
                        ...params.InputLabelProps,
                        sx: { fontSize: '0.75rem' }
                    }
                  }}
                />
              )}
            />
          </Box>

          {/* Date range: From and To */}
          <Box sx={{ gridColumn: 'span 1' }}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="From"
                value={filters.dateRange.start}
                onChange={(date) => handleDateRangeChange('start', date)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    size: "small",
                    variant: "outlined",
                    InputProps: {
                      sx: { fontSize: '0.75rem' },
                      endAdornment: filters.dateRange.start && (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDateRangeChange('start', null);
                            }}
                            title="Clear date"
                          >
                            <ClearIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      )
                    },
                    InputLabelProps: {
                      sx: { fontSize: '0.75rem' }
                    }
                  },
                  popper: {
                    sx: {
                      zIndex: 1,
                    }
                  }
                }}
              />
            </LocalizationProvider>
          </Box>

          <Box sx={{ gridColumn: 'span 1' }}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="To"
                value={filters.dateRange.end}
                onChange={(date) => handleDateRangeChange('end', date)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    size: "small",
                    variant: "outlined",
                    InputProps: {
                      sx: { fontSize: '0.75rem'},
                      endAdornment: filters.dateRange.end && (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDateRangeChange('end', null);
                            }}
                            title="Clear date"
                          >
                            <ClearIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      )
                    },
                    InputLabelProps: {
                      sx: { fontSize: '0.75rem' }
                    }
                  },
                  popper: {
                    sx: {
                      zIndex: 1,
                    }
                  }
                }}
              />
            </LocalizationProvider>
          </Box>
        </Box>

        {/* Right section: Location, Vehicle, and bottom controls - spans 6 columns */}
        <Box sx={{ gridColumn: 'span 6', display: 'flex', flexDirection: 'column', gap: 1 }}>
          {/* Location Zone and Delivery Vehicle in a row - same width as date field selector */}
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1, width: '100%' }}>
            {/* Location Zone */}
            <Box sx={{ gridColumn: 'span 1' }}>
              <Autocomplete
                size="small"
                options={locationZones}
                getOptionLabel={(option) => option.name}
                value={locationZones.find(zone => zone.name === filters.location) || null}
                onChange={(_, newValue) => onFilterChange('location', newValue?.name || '')}
                loading={loading.locationsLoading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Location Zone"
                    fullWidth
                    size="small"
                    variant="outlined"
                    sx={{ minHeight: '32px' }}
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        sx: { fontSize: '0.75rem'}
                      },
                      inputLabel: {
                        ...params.InputLabelProps,
                        sx: { fontSize: '0.75rem' }
                      }
                    }}
                  />
                )}
              />
            </Box>

            {/* Delivery Vehicle */}
            <Box sx={{ gridColumn: 'span 1' }}>
              <Autocomplete
                size="small"
                options={vehicleTypes}
                getOptionLabel={(option) => option.name}
                value={vehicleTypes.find(vehicle => vehicle.name === filters.deliveryVehicle) || null}
                onChange={(_, newValue) => onFilterChange('deliveryVehicle', newValue?.name || '')}
                loading={loading.vehicleTypesLoading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Delivery Vehicle"
                    fullWidth
                    size="small"
                    variant="outlined"
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        sx: { fontSize: '0.75rem', py: 0.125, minHeight: '32px' }
                      },
                      inputLabel: {
                        ...params.InputLabelProps,
                        sx: { fontSize: '0.75rem' }
                      }
                    }}
                  />
                )}
              />
            </Box>
          </Box>

          {/* Checkbox and Summary below Location/Vehicle */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, py: 0.25 }}>
            {/* Delivery date filter checkbox */}
            <FormControlLabel
              control={
                <Checkbox
                  checked={filters.hasDeliveryDate}
                  onChange={handleHasDeliveryDateChange}
                  color="primary"
                  size="small"
                  sx={{ py: 0.25 }}
                />
              }
              label="Has delivery date"
              sx={{
                '& .MuiFormControlLabel-label': {
                  fontSize: '0.75rem',
                  whiteSpace: 'nowrap'
                },
                margin: 0,
                flexShrink: 0
              }}
            />

            {/* Summary section */}
            {totals && (
              <Box sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 0.5,
                flex: 1,
                overflow: 'hidden',
              }}>
                <Typography variant="caption" sx={{
                  color: 'text.secondary',
                  fontWeight: 'medium',
                  fontSize: '0.7rem'
                }}>
                  <strong>Total CBM:</strong> {totals.totalCBM.toFixed(2)} &nbsp;
                  <strong>Total Pallets:</strong> {totals.totalPallets.toLocaleString()} &nbsp;
                  <strong>Total Pieces:</strong> {totals.totalPieces.toLocaleString()} &nbsp;
                  <strong>Total Inbound Pieces:</strong> {totals.totalInboundPieces.toLocaleString()}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>











        {/* Status filters - spans 12 columns */}
        <Box sx={{ gridColumn: 'span 12' }}>
          
          <Box sx={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: 0.5,
            maxHeight: '80px',
            overflow: 'auto',
          }}>
            {Object.values(ManifestStatus).map((status) => {
              const { bg, color } = getStatusChipColor(status);
              const isSelected = filters.status.includes(status);
              const count = totals?.byStatus?.[status] || 0;

              return (
                <Chip
                  key={status}
                  label={`${getStatusLabel(status)} (${count})`}
                  onClick={() => handleStatusFilterChange(status)}
                  sx={{
                    backgroundColor: isSelected ? bg : 'transparent',
                    color: isSelected ? color : 'text.secondary',
                    borderColor: isSelected ? 'transparent' : color,
                    fontWeight: isSelected ? 500 : 400,
                    '&:hover': {
                      backgroundColor: isSelected ? bg : `${bg}40`,
                    },
                    transition: 'all 0.2s ease',
                    height: '20px',
                    borderRadius: '10px',
                    border: isSelected ? 'none' : `1px solid ${color}40`,
                    fontSize: '0.7rem',
                    opacity: count > 0 ? 1 : 0.5
                  }}
                  variant={isSelected ? 'filled' : 'outlined'}
                  size="small"
                />
              );
            })}
          </Box>
        </Box>


          </Box>

          {/* Reset filters button */}
          {(filters.search || filters.status.length > 0 || filters.dateRange.start ||
            filters.dateRange.end || filters.location || filters.deliveryVehicle ||
            filters.hasDeliveryDate) && (
            <Box sx={{ mt: 0.5, display: 'flex', justifyContent: 'flex-start' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={onResetFilters}
                startIcon={<ClearAllIcon />}
                color="error"
                sx={{
                  borderRadius: 2,
                  fontSize: '0.75rem',
                  py: 0.25,
                  px: 1
                }}
              >
                Clear All Filters
              </Button>
            </Box>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default ManifestListFilters; 