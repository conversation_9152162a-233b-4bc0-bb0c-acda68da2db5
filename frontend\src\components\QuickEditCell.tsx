import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  Typography,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';

interface QuickEditCellProps {
  value: string | null | undefined;
  onSave: (newValue: string) => Promise<void>;
  placeholder?: string;
  maxLength?: number;
  multiline?: boolean;
  disabled?: boolean;
  fieldName?: string;
  maxLines?: number; // New prop to control line clamp
}

const QuickEditCell: React.FC<QuickEditCellProps> = ({
  value,
  onSave,
  placeholder = "Click to edit...",
  maxLength = 500,
  multiline = true,
  disabled = false,
  fieldName = "field",
  maxLines = 2 // Default to 2 lines for backward compatibility
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value || '');
  const [isSaving, setIsSaving] = useState(false);
  const textFieldRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  // Update edit value when prop value changes
  useEffect(() => {
    setEditValue(value || '');
  }, [value]);

  // Focus the text field when entering edit mode
  useEffect(() => {
    if (isEditing && textFieldRef.current) {
      const element = textFieldRef.current;

      // Use requestAnimationFrame to ensure the DOM has updated and the element is ready
      requestAnimationFrame(() => {
        element.focus();

        // Select all text for better UX - use another frame to ensure focus is complete
        requestAnimationFrame(() => {
          try {
            if (element.setSelectionRange) {
              // For both input and textarea elements
              element.setSelectionRange(0, element.value.length);
            } else if (typeof element.select === 'function') {
              // Fallback for input elements
              element.select();
            }
          } catch (error) {
            // Ignore selection errors - focus is more important
            console.debug('Text selection failed:', error);
          }
        });
      });
    }
  }, [isEditing]);

  const handleStartEdit = (event?: React.MouseEvent) => {
    if (disabled) return;

    // Prevent the click from propagating to the DataGrid row (which would select the row)
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    setIsEditing(true);
  };

  const handleCancel = () => {
    setEditValue(value || '');
    setIsEditing(false);
  };

  const handleSave = async () => {
    if (isSaving) return;
    
    try {
      setIsSaving(true);
      await onSave(editValue.trim());
      setIsEditing(false);
    } catch (error) {
      console.error(`Failed to save ${fieldName}:`, error);
      // Keep editing mode open on error
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleCancel();
    } else if (event.key === 'Enter' && !event.shiftKey && !multiline) {
      event.preventDefault();
      handleSave();
    } else if (event.key === 'Enter' && event.ctrlKey && multiline) {
      event.preventDefault();
      handleSave();
    }
  };

  if (isEditing) {
    return (
      <Box
        onClick={(e) => e.stopPropagation()} // Prevent row selection during editing
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: 0.5,
          width: '100%',
          minHeight: '40px'
        }}
      >
        <TextField
          inputRef={textFieldRef}
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleSave} // Auto-save when clicking away
          onClick={(e) => e.stopPropagation()} // Prevent row selection when clicking input
          onFocus={(e) => {
            // Select all text when field receives focus
            const element = e.target;
            requestAnimationFrame(() => {
              try {
                if (element.setSelectionRange) {
                  element.setSelectionRange(0, element.value.length);
                } else if (typeof element.select === 'function') {
                  element.select();
                }
              } catch (error) {
                console.debug('Text selection on focus failed:', error);
              }
            });
          }}
          autoFocus // Ensure immediate focus when component mounts
          multiline={multiline}
          rows={multiline ? 2 : 1}
          size="small"
          variant="outlined"
          placeholder={placeholder}
          inputProps={{ maxLength }}
          disabled={isSaving}
          sx={{ 
            flexGrow: 1,
            '& .MuiOutlinedInput-root': {
              fontSize: '0.875rem',
            }
          }}
        />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Tooltip title={multiline ? "Save (Ctrl+Enter)" : "Save (Enter)"}>
            <IconButton
              size="small"
              color="primary"
              onClick={(e) => {
                e.stopPropagation(); // Prevent row selection
                handleSave();
              }}
              disabled={isSaving}
              sx={{ minWidth: '24px', height: '24px' }}
            >
              {isSaving ? (
                <CircularProgress size={16} />
              ) : (
                <SaveIcon fontSize="small" />
              )}
            </IconButton>
          </Tooltip>
          <Tooltip title="Cancel (Esc)">
            <IconButton
              size="small"
              color="secondary"
              onClick={(e) => {
                e.stopPropagation(); // Prevent row selection
                handleCancel();
              }}
              disabled={isSaving}
              sx={{ minWidth: '24px', height: '24px' }}
            >
              <CancelIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      onClick={(e) => {
        e.stopPropagation(); // Prevent row selection
        handleStartEdit(e);
      }}
      sx={{
        display: 'flex',
        alignItems: 'flex-start', // Changed from 'center' to 'flex-start' for better text alignment
        width: '100%',
        minHeight: '40px', // Increased from 40px to accommodate more lines
        cursor: disabled ? 'default' : 'pointer',
        padding: '8px', // Increased padding for better spacing
        borderRadius: '4px',
        '&:hover': disabled ? {} : {
          backgroundColor: 'rgba(0, 0, 0, 0.04)',
        },
        position: 'relative',
        group: 'quick-edit-cell',
        // Ensure the box takes full width and height
        boxSizing: 'border-box',
        flex: 1
      }}
    >
      <Typography
        variant="body2"
        sx={{
          textAlign: 'left',
          whiteSpace: 'normal',
          wordBreak: 'break-word',
          lineHeight: '1.4',
          width: '100%',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: maxLines,
          WebkitBoxOrient: 'vertical',
          color: value ? 'text.primary' : 'text.secondary',
          fontStyle: value ? 'normal' : 'italic'
        }}
      >
        {value || placeholder}
      </Typography>
      {!disabled && (
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation(); // Prevent row selection
            handleStartEdit(e);
          }}
          sx={{
            position: 'absolute',
            top: '2px',
            right: '2px',
            opacity: 0,
            transition: 'opacity 0.2s',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 1)',
            },
            '.group:hover &': {
              opacity: 1,
            }
          }}
        >
          <EditIcon fontSize="small" />
        </IconButton>
      )}
    </Box>
  );
};

export default QuickEditCell;
