import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Alert,
  Paper,
  CircularProgress,
  Chip,
  IconButton,
  useTheme,
  Breadcrumbs,
  Link,
  alpha,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  InputAdornment,
  LinearProgress,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider,
  Stack
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import DirectionsBoatOutlinedIcon from '@mui/icons-material/DirectionsBoatOutlined';
import TimelineIcon from '@mui/icons-material/Timeline';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import ConstructionIcon from '@mui/icons-material/Construction';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import RefreshIcon from '@mui/icons-material/Refresh';
import HomeIcon from '@mui/icons-material/Home';
import PersonIcon from '@mui/icons-material/Person';
import AddIcon from '@mui/icons-material/Add';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloseIcon from '@mui/icons-material/Close';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import InfoIcon from '@mui/icons-material/Info';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { useAuth } from '../contexts/AuthContext';
import usePageTitle from '../hooks/usePageTitle';
import containerService from '../services/container.service';
import manifestService from '../services/manifest.service';
import locationService from '../services/location.service';
import * as vehicleService from '../services/vehicle.service';
import userService from '../services/user.service';
import { Container, ContainerStatus } from '../types/Container';
import { Manifest, ManifestStatus } from '../types/manifest';
import { LocationZone } from '../types/location';
import { VehicleType } from '../types/Vehicle';
import { Client, Driver } from '../types/User';
import { formatDateString, formatDateOnly, getTimeSlotIdFromDate } from '../utils/dateUtils';
import { useToast } from '../contexts/ToastContext';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import SplitDeliveryDatePicker from '../components/SplitDeliveryDatePicker';
import * as Excel from 'exceljs';
import ManifestList from '../components/ManifestList';

const ContainerDetailTest: React.FC = () => {
  const { containerNo } = useParams<{ containerNo: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const toast = useToast();

  // Set page title with container number
  usePageTitle(containerNo ? `Container ${containerNo}` : 'Container Detail');

  const [container, setContainer] = useState<Container | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Manifest list related states
  const [manifests, setManifests] = useState<Manifest[]>([]);
  const [manifestLoading, setManifestLoading] = useState(false);
  const [manifestError, setManifestError] = useState<string | null>(null);
  
  // Resource states for ManifestList component
  const [locationZones, setLocationZones] = useState<LocationZone[]>([]);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(false);
  const [vehicleTypesLoading, setVehicleTypesLoading] = useState(false);

  // Additional resource states for create manifest
  const [clients, setClients] = useState<Client[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);

  // Bulk upload states
  const [bulkUploadDialogOpen, setBulkUploadDialogOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [bulkUploadResults, setBulkUploadResults] = useState<{
    total: number;
    successful: number;
    failed: number;
    errors: Array<{ row: number; error: string }>;
  } | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Create manifest states
  const [openManifestDialog, setOpenManifestDialog] = useState(false);
  const [manifestFormData, setManifestFormData] = useState<any>({
    trackingNo: '',
    client: { username: '' },
    container: { containerNo: '' },
    driver: { username: '' },
    sequenceNo: 0,
    status: ManifestStatus.CREATED,
    customerName: '',
    phoneNo: '',
    address: '',
    postalCode: '',
    country: 'Singapore',
    pieces: 0,
    cbm: 0,
    actualPalletsCount: 0,
    location: '',
    deliveryDate: null,
    timeSlot: null,
    deliveryVehicle: '',
    driverRemarks: '',
    remarks: '',
    weight: 0,
    createdDate: null,
  });
  const [manifestFormLoading, setManifestFormLoading] = useState(false);
  const [manifestFormError, setManifestFormError] = useState<string | null>(null);

  // Container status update states
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<ContainerStatus | null>(null);

  useEffect(() => {
    if (!containerNo) {
      setError('Container number is required');
      setLoading(false);
      return;
    }
    
    // Reset states when container number changes
    setContainer(null);
    setError(null);
    setLoading(true);
    
    fetchContainerDetail();
    fetchContainerManifests();
    fetchLocationZones();
    fetchVehicleTypes();
    fetchClientsAndDrivers();
  }, [containerNo, currentUser?.token]);

  const fetchContainerDetail = async () => {
    if (!currentUser?.token || !containerNo) {
      setError('Authentication required');
      setLoading(false);
      return;
    }
    
    try {
      console.log(`Fetching details for container: ${containerNo}`);
      setLoading(true);
      setError(null);
      
      const containerResponse = await containerService.getContainerByNo(containerNo, currentUser.token);
      
      if (containerResponse.success) {
        console.log("Container details fetched successfully:", containerResponse.data);
        setContainer(containerResponse.data);
      } else {
        console.error("Failed to fetch container details:", containerResponse.message);
        setError(containerResponse.message || 'Failed to fetch container details');
      }
    } catch (err: any) {
      console.error('Error fetching container details:', err);
      setError(err.response?.data?.message || 'An error occurred while fetching data');
    } finally {
      setLoading(false);
    }
  };

  const fetchContainerManifests = async () => {
    if (!currentUser?.token || !containerNo) {
      setManifestError('Authentication required');
      return;
    }
    
    try {
      console.log(`Fetching manifests for container ${containerNo}...`);
      setManifestLoading(true);
      setManifestError(null);
      
      const response = await manifestService.getManifestsByContainer(containerNo, currentUser.token);
      
      if (response.success) {
        console.log(`Successfully fetched ${response.data.length} manifests for container ${containerNo}`);
        setManifests(response.data);
      } else {
        console.error("Failed to fetch manifests:", response.message);
        setManifestError(response.message || 'Failed to fetch manifests');
      }
    } catch (err: any) {
      console.error('Error fetching manifests:', err);
      setManifestError(err.message || 'An error occurred while fetching manifests');
    } finally {
      setManifestLoading(false);
    }
  };

  const fetchLocationZones = async () => {
    if (!currentUser?.token) {
      return;
    }
    
    try {
      setLocationsLoading(true);
      const response = await locationService.getActiveLocationZones(currentUser.token);
      
      if (response.success) {
        console.log("Location zones fetched successfully:", response.data.length);
        setLocationZones(response.data);
      } else {
        console.error("Failed to fetch location zones:", response.message);
      }
    } catch (err) {
      console.error('Error fetching location zones:', err);
    } finally {
      setLocationsLoading(false);
    }
  };

  const fetchVehicleTypes = async () => {
    if (!currentUser?.token) {
      return;
    }

    try {
      setVehicleTypesLoading(true);
      const data = await vehicleService.getVehicleTypes();
      console.log("Vehicle types fetched successfully:", data.length);
      setVehicleTypes(data);
    } catch (err) {
      console.error('Error fetching vehicle types:', err);
    } finally {
      setVehicleTypesLoading(false);
    }
  };

  const fetchClientsAndDrivers = async () => {
    if (!currentUser?.token) {
      return;
    }

    try {
      // Fetch clients
      const clientsResponse = await userService.getClients(currentUser.token);
      if (clientsResponse.success) {
        console.log("Clients fetched successfully:", clientsResponse.data.length);
        setClients(clientsResponse.data);
      } else {
        console.error("Failed to fetch clients:", clientsResponse.message);
      }

      // Fetch drivers
      const driversResponse = await userService.getDrivers(currentUser.token);
      if (driversResponse.success) {
        console.log("Drivers fetched successfully:", driversResponse.data.length);
        setDrivers(driversResponse.data);
      } else {
        console.error("Failed to fetch drivers:", driversResponse.message);
      }
    } catch (err) {
      console.error('Error fetching clients and drivers:', err);
    }
  };

  const formatDateTime = (dateStr?: string | null) => {
    return formatDateString(dateStr);
  };
  
  const getStatusChipColor = (status: ContainerStatus) => {
    const defaultColors = { bg: 'rgba(158, 158, 158, 0.1)', color: 'text.secondary' };
    
    const containerStatus = status as ContainerStatus;
    switch (containerStatus) {
      case ContainerStatus.CREATED:
        return { bg: 'rgba(255, 167, 38, 0.1)', color: 'warning.main' };
      case ContainerStatus.CONFIRMED:
        return { bg: 'rgba(33, 150, 243, 0.1)', color: 'info.main' };
      case ContainerStatus.ARRIVED:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.UNSTUFFING:
        return { bg: 'rgba(255, 193, 7, 0.1)', color: 'warning.main' };
      case ContainerStatus.UNSTUFF_COMPLETED:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.READY_TO_PULL_OUT:
        return { bg: 'rgba(0, 200, 83, 0.1)', color: 'success.main' };
      case ContainerStatus.PULLED_OUT:
        return { bg: 'rgba(76, 175, 80, 0.1)', color: 'success.dark' };
      default:
        return defaultColors;
    }
  };

  const getContainerStatusIcon = (status: ContainerStatus) => {
    switch (status) {
      case ContainerStatus.CREATED:
        return <CalendarTodayIcon sx={{ color: theme.palette.warning.main }} />;
      case ContainerStatus.CONFIRMED:
        return <CheckCircleIcon sx={{ color: theme.palette.info.main }} />;
      case ContainerStatus.ARRIVED:
        return <DirectionsBoatIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.UNSTUFFING:
        return <ConstructionIcon sx={{ color: theme.palette.warning.main }} />;
      case ContainerStatus.UNSTUFF_COMPLETED:
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.READY_TO_PULL_OUT:
        return <LocalShippingOutlinedIcon sx={{ color: theme.palette.success.main }} />;
      case ContainerStatus.PULLED_OUT:
        return <LocalShippingIcon sx={{ color: theme.palette.success.dark }} />;
      default:
        return <TimelineIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };

  // Manifest list callback handlers
  const handleManifestUpdate = async (_manifest: Manifest) => {
    // Refresh the manifest list after update
    await fetchContainerManifests();
  };

  const handleManifestDelete = async (trackingNo: string) => {
    try {
      if (!currentUser?.token) {
        throw new Error('Authentication token not available');
      }

      const response = await manifestService.deleteManifest(trackingNo, currentUser.token);

      if (response.success) {
        toast.success(`Manifest ${trackingNo} deleted successfully`);
        // Refresh the manifest list after deletion
        await fetchContainerManifests();
      } else {
        throw new Error(response.message || 'Failed to delete manifest');
      }
    } catch (error: any) {
      console.error('Error deleting manifest:', error);
      toast.error(error.message || 'Failed to delete manifest');
      throw error; // Re-throw to let the dialog handle the error state
    }
  };

  const handleBulkDelete = async (trackingNos: string[]) => {
    try {
      if (!currentUser?.token) {
        throw new Error('Authentication token not available');
      }

      console.log(`🗑️ ContainerDetail: Starting bulk deletion of ${trackingNos.length} manifests:`, trackingNos);

      // Delete manifests one by one
      const promises = trackingNos.map(trackingNo =>
        manifestService.deleteManifest(trackingNo, currentUser.token!)
      );

      const results = await Promise.allSettled(promises);

      // Count successful deletions
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.length - successful;

      console.log(`📊 ContainerDetail: Bulk deletion results - ${successful} successful, ${failed} failed`);

      if (successful > 0) {
        toast.success(`Successfully deleted ${successful} manifest(s) from container ${containerNo}`);
      }

      if (failed > 0) {
        toast.error(`Failed to delete ${failed} manifest(s)`);
      }

      // Refresh the manifest list after bulk deletion
      await fetchContainerManifests();

    } catch (error: any) {
      console.error('Error in bulk delete:', error);
      toast.error(error.message || 'Failed to delete manifests');
      throw error; // Re-throw to let the dialog handle the error state
    }
  };

  const handleRefresh = async () => {
    await Promise.all([
      fetchContainerDetail(),
      fetchContainerManifests()
    ]);
  };

  // Handler for saving container status update
  const handleSaveStatus = async () => {
    if (!container || !newStatus || !currentUser?.token) {
      return;
    }

    try {
      console.log(`Updating container ${container.containerNo} status to ${newStatus}`);

      // Update the container status
      const response = await containerService.updateContainerStatus(
        container.containerNo,
        newStatus,
        currentUser.token
      );

      if (response.success) {
        toast.success(`Container status updated to ${newStatus}`);

        // Close the dialog
        setStatusDialogOpen(false);

        // Clear the new status
        setNewStatus(null);

        // Refresh container data and manifests
        await fetchContainerDetail();
        await fetchContainerManifests();
      } else {
        toast.error(response.message || 'Failed to update status');
      }
    } catch (err: any) {
      console.error('Error updating container status:', err);
      toast.error(err.response?.data?.message || 'An error occurred while updating status');
    }
  };

  // Bulk upload handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setBulkUploadResults(null);
      setUploadProgress(0);
      setActiveStep(1);
    }
  };

  const handleResetFileUpload = () => {
    setUploadedFile(null);
    setUploadProgress(0);
    setBulkUploadResults(null);
    setActiveStep(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleOpenBulkUpload = () => {
    setBulkUploadDialogOpen(true);
    setActiveStep(0);
    setBulkUploadResults(null);
    setUploadedFile(null);
    setUploadProgress(0);
  };

  // File handling functions
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      setBulkUploadResults(null);
      setUploadProgress(0);
    }
  };

  const handleFileDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.xls'))) {
      setUploadedFile(file);
      setBulkUploadResults(null);
      setUploadProgress(0);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  // Excel parsing helper functions
  const parseRowsArrayData = async (worksheet: Excel.Worksheet) => {
    const results: any[] = [];
    const errors: Array<{ row: number; error: string }> = [];

    // Skip header row (row 1) and start from row 2
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // Skip empty rows
      if (!row.hasValues) continue;

      // Helper functions for cell value extraction
      const getCellStringValue = (colIndex: number, fieldName: string): string => {
        const cell = row.getCell(colIndex);
        const value = cell.value;

        if (value === null || value === undefined) return '';

        if (typeof value === 'string') return value.trim();
        if (typeof value === 'number') return value.toString();
        if (value instanceof Date) return value.toISOString().split('T')[0];

        // Handle rich text or formula results
        if (typeof value === 'object' && 'richText' in value) {
          return value.richText?.map((rt: any) => rt.text).join('') || '';
        }

        return String(value).trim();
      };

      const getCellNumericValue = (colIndex: number, fieldName: string): string => {
        const cell = row.getCell(colIndex);
        const value = cell.value;

        if (value === null || value === undefined) return '';
        if (typeof value === 'number') return value.toString();
        if (typeof value === 'string') {
          const trimmed = value.trim();
          return trimmed === '' ? '' : trimmed;
        }

        return String(value).trim();
      };

      try {
        // Map the row data to our structure (container-specific template)
        const rowData = {
          trackingNo: getCellStringValue(1, 'Tracking'),
          customerName: getCellStringValue(2, 'Cust'),
          address: getCellStringValue(3, 'Address'),
          postalCode: getCellStringValue(4, 'Postal Code'),
          phoneNo: getCellStringValue(5, 'Phone'),
          pieces: getCellNumericValue(6, 'Pcs'),
          cbm: getCellNumericValue(7, 'CBM'),
          driverRemarks: getCellStringValue(8, 'For Driver Only'),
          remarks: getCellStringValue(9, 'For CS Only'),
          weight: getCellNumericValue(10, 'Weight'),
        };

        // Validation
        const validation = validateRowData(rowData, rowNumber);

        if (validation.isValid) {
          // Convert to Manifest object with container constraints
          const manifest: Partial<Manifest> = {
            trackingNo: rowData.trackingNo,
            client: { username: container?.client?.username || '' } as Client,
            container: { containerNo: containerNo || '' } as Container,
            customerName: rowData.customerName,
            phoneNo: rowData.phoneNo,
            address: rowData.address,
            postalCode: rowData.postalCode,
            country: 'Singapore', // Default to Singapore for container manifests
            pieces: parseInt(rowData.pieces) || 0,
            cbm: parseFloat(rowData.cbm) || 0,
            weight: rowData.weight ? parseFloat(rowData.weight) || 0 : 0,
            driverRemarks: rowData.driverRemarks || '',
            remarks: rowData.remarks || '',
            status: ManifestStatus.CREATED
          };

          results.push(manifest);
        } else {
          errors.push(...validation.errors);
        }
      } catch (error: any) {
        errors.push({
          row: rowNumber,
          error: `Unexpected error processing row: ${error.message}`
        });
      }
    }

    return { results, errors };
  };

  const validateRowData = (rowData: any, rowNumber: number) => {
    const errors: Array<{ row: number; error: string }> = [];

    // Required field validation
    const requiredFields = [
      { field: 'trackingNo', name: 'Tracking' },
      { field: 'customerName', name: 'Cust' },
      { field: 'address', name: 'Address' },
      { field: 'postalCode', name: 'Postal Code' },
      { field: 'phoneNo', name: 'Phone' },
      { field: 'pieces', name: 'Pcs' },
      { field: 'cbm', name: 'CBM' }
    ];

    for (const { field, name } of requiredFields) {
      if (!rowData[field] || rowData[field].toString().trim() === '') {
        errors.push({
          row: rowNumber,
          error: `Missing required field: ${name}`
        });
      }
    }

    // Numeric validation
    if (rowData.pieces && isNaN(parseInt(rowData.pieces))) {
      errors.push({
        row: rowNumber,
        error: 'Pieces must be a valid number'
      });
    }

    if (rowData.cbm && isNaN(parseFloat(rowData.cbm))) {
      errors.push({
        row: rowNumber,
        error: 'CBM must be a valid number'
      });
    }

    if (rowData.weight && rowData.weight !== '' && isNaN(parseFloat(rowData.weight))) {
      errors.push({
        row: rowNumber,
        error: 'Weight must be a valid number'
      });
    }

    // Container context validation
    if (!containerNo) {
      errors.push({
        row: rowNumber,
        error: 'Container context is missing. Please refresh the page and try again.'
      });
    }

    if (!container?.client?.username) {
      errors.push({
        row: rowNumber,
        error: 'Container client information is missing. Please refresh the page and try again.'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const handleFileUpload = async () => {
    if (!uploadedFile || !currentUser?.token) {
      toast.error('Please select a file to upload');
      return;
    }

    setIsUploading(true);
    setUploadProgress(10);
    setBulkUploadResults(null);

    try {
      const arrayBuffer = await uploadedFile.arrayBuffer();
      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(arrayBuffer);

      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) {
        throw new Error('No worksheet found in the uploaded file');
      }

      setUploadProgress(30);

      const { results: manifests, errors } = await parseRowsArrayData(worksheet);

      setUploadProgress(50);

      let successCount = 0;

      if (errors.length > 0) {
        console.log(`Bulk upload cancelled: ${errors.length} validation errors found. No manifests will be created.`);
      } else if (manifests.length > 0) {
        try {
          setUploadProgress(70);

          const response = await manifestService.createManifestsBulk(
            manifests,
            currentUser.token
          );

          if (response.success && response.data) {
            successCount = response.data.length;
            console.log(`Bulk creation successful: ${successCount} manifests created for container ${containerNo}`);
          } else {
            errors.push({
              row: 0,
              error: response.message || 'Bulk creation failed'
            });
          }
        } catch (err: any) {
          let errorMessage = 'Bulk creation failed';
          if (err.response?.data?.message) {
            errorMessage = err.response.data.message;
          } else if (err.message) {
            errorMessage = err.message;
          }

          errors.push({
            row: 0,
            error: errorMessage
          });
          console.error('Bulk manifest creation failed:', err);
        }
      }

      setUploadProgress(100);

      setBulkUploadResults({
        total: manifests.length + errors.filter(e => e.row > 0).length,
        successful: successCount,
        failed: errors.length,
        errors
      });

      if (successCount > 0) {
        toast.success(`Created ${successCount} manifests successfully for container ${containerNo}`);
        fetchContainerManifests();
      }

      setActiveStep(2);

    } catch (error: any) {
      console.error('Error processing file:', error);
      toast.error('Error processing file: ' + error.message);
      setBulkUploadResults({
        total: 0,
        successful: 0,
        failed: 1,
        errors: [{ row: 0, error: error.message }]
      });
      setActiveStep(2);
    } finally {
      setIsUploading(false);
    }
  };

  // Add debug function for Excel file analysis (matching ManifestListTest)
  const debugExcelFile = async (file: File) => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(arrayBuffer);

      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) {
        console.log('❌ No worksheet found');
        return;
      }

      console.log('📊 Excel Debug Analysis:');
      console.log(`📄 Worksheet name: ${worksheet.name}`);
      console.log(`📏 Row count: ${worksheet.rowCount}`);
      console.log(`📐 Column count: ${worksheet.columnCount}`);

      // Log header row
      const headerRow = worksheet.getRow(1);
      const headers: string[] = [];
      headerRow.eachCell((cell, colNumber) => {
        headers.push(`Col ${colNumber}: "${cell.value}"`);
      });
      console.log('📋 Headers:', headers);

      // Log first few data rows
      for (let i = 2; i <= Math.min(5, worksheet.rowCount); i++) {
        const row = worksheet.getRow(i);
        const rowData: string[] = [];
        row.eachCell((cell, colNumber) => {
          rowData.push(`Col ${colNumber}: "${cell.value}"`);
        });
        console.log(`📝 Row ${i}:`, rowData);
      }
    } catch (error) {
      console.error('❌ Debug analysis failed:', error);
    }
  };

  const downloadTemplate = async () => {
    try {
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Manifest Template');

      // Define template columns (container-specific, no container/client columns needed)
      const columns = [
        { header: 'Tracking', key: 'trackingNo', width: 20 },
        { header: 'Cust', key: 'customerName', width: 25 },
        { header: 'Address', key: 'address', width: 35 },
        { header: 'Postal Code', key: 'postalCode', width: 15 },
        { header: 'Phone', key: 'phoneNo', width: 18 },
        { header: 'Pcs', key: 'pieces', width: 12 },
        { header: 'CBM', key: 'cbm', width: 12 },
        { header: 'For Driver Only', key: 'driverRemarks', width: 30 },
        { header: 'For CS Only', key: 'remarks', width: 30 },
        { header: 'Weight (kg)', key: 'weight', width: 15 },
      ];

      worksheet.columns = columns;

      // Add one sample row to demonstrate data entry
      const sampleRow = {
        trackingNo: 'TRK12345',
        customerName: 'John Doe',
        address: '123 Orchard Road, #12-34, Singapore',
        postalCode: '238874',
        phoneNo: '+65 9123 4567',
        pieces: 5,
        cbm: 2.5,
        driverRemarks: 'Fragile items',
        remarks: 'Handle with care',
        weight: 100,
      };

      // Apply the sample data to the first data row (row 2)
      const firstDataRow = worksheet.getRow(2);
      firstDataRow.getCell(1).value = sampleRow.trackingNo;
      firstDataRow.getCell(2).value = sampleRow.customerName;
      firstDataRow.getCell(3).value = sampleRow.address;
      firstDataRow.getCell(4).value = sampleRow.postalCode;
      firstDataRow.getCell(5).value = sampleRow.phoneNo;
      firstDataRow.getCell(6).value = sampleRow.pieces;
      firstDataRow.getCell(7).value = sampleRow.cbm;
      firstDataRow.getCell(8).value = sampleRow.driverRemarks;
      firstDataRow.getCell(9).value = sampleRow.remarks;
      firstDataRow.getCell(10).value = sampleRow.weight;

      // Style the sample data cells
      for (let col = 1; col <= 10; col++) {
        const cell = firstDataRow.getCell(col);
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE6F3FF' }
        };
        cell.font = { italic: true };
      }

      // Add instructions
      worksheet.addRow(['']);
      worksheet.addRow(['Instructions:']);
      worksheet.addRow(['1. Fields marked with * are required']);
      worksheet.addRow(['2. Weight is optional - will default to 0 if empty']);
      worksheet.addRow([`3. All manifests will be created for container: ${containerNo}`]);
      worksheet.addRow([`4. All manifests will be assigned to client: ${container?.client?.companyName || container?.client?.username || 'N/A'}`]);
      worksheet.addRow(['5. Country will automatically be set to Singapore for all manifests']);
      worksheet.addRow(['6. Delete the sample row before uploading your data']);
      worksheet.addRow(['7. All validation errors must be fixed before any manifests are created']);

      // Style the header row
      const headerRow = worksheet.getRow(1);
      headerRow.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FF4472C4' }
        };
        cell.font = {
          color: { argb: 'FFFFFFFF' },
          bold: true
        };
        cell.alignment = {
          horizontal: 'center',
          vertical: 'middle'
        };
      });

      // Generate and download the file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `manifest-template-${containerNo}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Template downloaded successfully');
    } catch (error: any) {
      console.error('Error generating template:', error);
      toast.error('Failed to generate template: ' + error.message);
    }
  };

  // Create manifest handlers
  const handleOpenManifestDialog = async () => {
    if (container) {
      setManifestFormLoading(true);

      // Reset form data with container and client information
      setManifestFormData({
        trackingNo: '',
        client: container.client || { username: '' },
        container: { containerNo: containerNo || '' },
        driver: { username: '' },
        sequenceNo: 0,
        status: ManifestStatus.CREATED,
        customerName: '',
        phoneNo: '',
        address: '',
        postalCode: '',
        country: 'Singapore',
        pieces: 0,
        cbm: 0,
        actualPalletsCount: 0,
        location: '',
        deliveryDate: null,
        timeSlot: null,
        deliveryVehicle: '',
        driverRemarks: '',
        remarks: '',
        weight: 0,
        createdDate: new Date().toISOString()
      });

      try {
        // Fetch the next sequence number for the container
        if (currentUser?.token) {
          const response = await manifestService.getNextSequenceNumber(containerNo || '', currentUser.token);
          if (response.success) {
            setManifestFormData((prevData: any) => ({
              ...prevData,
              sequenceNo: response.data
            }));
            console.log(`Auto-assigned sequence number ${response.data} for new manifest`);
          }
        }
      } catch (error) {
        console.error('Error fetching next sequence number:', error);
      } finally {
        setManifestFormLoading(false);
        setOpenManifestDialog(true);
      }
    }
  };

  const handleCloseManifestDialog = () => {
    setOpenManifestDialog(false);
    setManifestFormError(null);
  };

  const handleManifestFormChange = (field: string, value: any) => {
    setManifestFormData((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveManifest = async () => {
    try {
      setManifestFormLoading(true);
      setManifestFormError(null);

      // Basic validation
      if (!manifestFormData.customerName) {
        setManifestFormError('Customer name is required');
        setManifestFormLoading(false);
        return;
      }

      if (!manifestFormData.address) {
        setManifestFormError('Address is required');
        setManifestFormLoading(false);
        return;
      }

      if (!manifestFormData.postalCode) {
        setManifestFormError('Postal code is required');
        setManifestFormLoading(false);
        return;
      }

      if (!manifestFormData.phoneNo) {
        setManifestFormError('Phone number is required');
        setManifestFormLoading(false);
        return;
      }

      if (!manifestFormData.pieces || manifestFormData.pieces <= 0) {
        setManifestFormError('Number of pieces must be greater than 0');
        setManifestFormLoading(false);
        return;
      }

      if (!manifestFormData.trackingNo || manifestFormData.trackingNo.trim() === '') {
        setManifestFormError('Tracking number is required');
        setManifestFormLoading(false);
        return;
      }

      const manifestToSave = {
        trackingNo: manifestFormData.trackingNo.trim(),
        client: { username: container?.client?.username || '' },
        container: { containerNo: containerNo },
        driver: { username: manifestFormData.driver?.username || '' },
        sequenceNo: manifestFormData.sequenceNo,
        status: manifestFormData.status,
        customerName: manifestFormData.customerName.trim(),
        phoneNo: manifestFormData.phoneNo.trim(),
        address: manifestFormData.address.trim(),
        postalCode: manifestFormData.postalCode?.trim(),
        country: manifestFormData.country?.trim() || 'Singapore',
        pieces: Number(manifestFormData.pieces),
        cbm: Number(manifestFormData.cbm || 0),
        weight: Number(manifestFormData.weight || 0),
        actualPalletsCount: manifestFormData.actualPalletsCount ? Number(manifestFormData.actualPalletsCount) : 0,
        location: manifestFormData.location?.trim() || '',
        deliveryDate: manifestFormData.deliveryDate ? formatDateOnly(manifestFormData.deliveryDate) : null,
        deliveryVehicle: manifestFormData.deliveryVehicle?.trim() || null,
        driverRemarks: manifestFormData.driverRemarks?.trim() || '',
        remarks: manifestFormData.remarks?.trim() || ''
      };

      const token = currentUser?.token;
      if (!token) {
        setManifestFormError('Authentication token not found');
        setManifestFormLoading(false);
        return;
      }

      console.log('Attempting to create manifest with data:', manifestToSave);

      const response = await manifestService.createManifest(manifestToSave as any, token);

      if (response.success) {
        toast.success('Manifest created successfully');
        handleCloseManifestDialog();
        fetchContainerManifests();
      } else {
        setManifestFormError(response.message || 'Failed to create manifest');
      }
    } catch (error: any) {
      console.error('Error creating manifest:', error);
      const errorMsg = error.response?.data?.message ||
                      (error.message === 'Request failed with status code 500' ?
                       'Server error: There may be an issue with the data format. Please check all fields.' :
                       error.message) ||
                      'An unexpected error occurred';

      setManifestFormError(errorMsg);
    } finally {
      setManifestFormLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button
          variant="contained"
          onClick={() => navigate('/containers')}
          startIcon={<ArrowBackIcon />}
        >
          Back to Containers
        </Button>
      </Box>
    );
  }

  if (!container) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Container not found
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link
          component="button"
          variant="body1"
          onClick={() => navigate('/containers')}
          sx={{ textDecoration: 'none', color: 'primary.main' }}
        >
          Containers
        </Link>
        <Typography color="text.primary">
          {container.containerNo}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton
            onClick={() => navigate('/containers')}
            sx={{ mr: 1 }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1">
            Container {container.containerNo}
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
        >
          Refresh
        </Button>
      </Box>

      {/* Container Information Card */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <DirectionsBoatOutlinedIcon />
          Container Information
        </Typography>

        <Box sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' },
          gap: 3
        }}>
          {/* Basic Container Details (first row) */}
          <Box>
            <Typography variant="body2" color="text.secondary">Container No</Typography>
            <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
              {container.containerNo}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Client</Typography>
            <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {container.client?.companyName || '-'}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Status</Typography>
            <Typography
              variant="body1"
              component="div"
              sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}
            >
              <Tooltip title="Click to update container status">
                <Chip
                  icon={getContainerStatusIcon(container.status)}
                  label={container.status}
                  size="small"
                  sx={{
                    ...getStatusChipColor(container.status),
                    fontWeight: 'medium',
                    fontSize: '0.75rem',
                    cursor: 'pointer'
                  }}
                  onClick={() => {
                    setNewStatus(container?.status || null);
                    setStatusDialogOpen(true);
                  }}
                />
              </Tooltip>
            </Typography>
          </Box>

          {/* Transport Information (second row) */}
          <Box>
            <Typography variant="body2" color="text.secondary">Truck No</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <LocalShippingIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {container.truckNo || '-'}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Vessel Voyage No</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <DirectionsBoatOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {container.vesselVoyageNo || '-'}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Manifest Quantity</Typography>
            <Typography variant="body1" sx={{ fontWeight: 500, mt: 0.5 }}>
              {container.manifestQuantity || '0'}
            </Typography>
          </Box>

          {/* Date Information (third row) */}
          <Box>
            <Typography variant="body2" color="text.secondary">Created Date</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <CalendarTodayIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.createdDate)}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">ETA Requested</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <EventAvailableIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.etaRequestedDate)}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Portnet ETA</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <DirectionsBoatIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.portnetEta)}
            </Typography>
          </Box>

          {/* More Date Information (fourth row) */}
          <Box>
            <Typography variant="body2" color="text.secondary">ETA Allocated</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <TimelineIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.etaAllocated)}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Confirmed Date</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <CheckCircleIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.confirmedDate)}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Arrival Date</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <LocalShippingIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.arrivalDate)}
            </Typography>
          </Box>

          {/* Fifth row */}
          <Box>
            <Typography variant="body2" color="text.secondary">Loading Bay</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <WarehouseIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {container.loadingBay || '-'}
            </Typography>
          </Box>

          {/* Unstuffing Information (fifth row) */}
          <Box>
            <Typography variant="body2" color="text.secondary">Unstuff Date</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <ConstructionIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.unstuffDate)}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Unstuff Completed Date</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <CheckCircleIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.unstuffCompletedDate)}
            </Typography>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">Pull Out Date</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <LocalShippingOutlinedIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {formatDateTime(container.pullOutDate)}
            </Typography>
          </Box>

          {/* Unstuff Team (sixth row) */}
          <Box>
            <Typography variant="body2" color="text.secondary">Unstuff Team</Typography>
            <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <PersonIcon sx={{ mr: 1, color: theme.palette.grey[600], fontSize: 'small' }} />
              {container.unstuffTeam || '-'}
            </Typography>
          </Box>
        </Box>

        {/* Remarks Section */}
        {container.remark && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="body2" color="text.secondary">Remarks</Typography>
            <Typography variant="body1" sx={{ mt: 0.5 }}>
              {container.remark}
            </Typography>
          </Box>
        )}

        {/* ETA Rejection Information */}
        {container.etaRejected && (
          <Box sx={{ mt: 3, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
            <Typography variant="body2" color="error.dark" sx={{ fontWeight: 'medium' }}>
              ETA Rejected
            </Typography>
            {container.etaRejectionReason && (
              <Typography variant="body2" color="error.dark" sx={{ mt: 0.5 }}>
                Reason: {container.etaRejectionReason}
              </Typography>
            )}
          </Box>
        )}
      </Paper>

      {/* Container Summary Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <TimelineIcon />
          Container Summary
        </Typography>

        <Box sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
          gap: 2
        }}>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 1 }}>
            <Typography variant="h4" color="primary.contrastText" sx={{ fontWeight: 'bold' }}>
              {manifests.length}
            </Typography>
            <Typography variant="body2" color="primary.contrastText">
              Total Manifests
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
            <Typography variant="h4" color="success.contrastText" sx={{ fontWeight: 'bold' }}>
              {manifests.filter(m => m.status === ManifestStatus.DELIVERED).length}
            </Typography>
            <Typography variant="body2" color="success.contrastText">
              Delivered
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
            <Typography variant="h4" color="warning.contrastText" sx={{ fontWeight: 'bold' }}>
              {manifests.filter(m =>
                m.status === ManifestStatus.PENDING_DELIVER ||
                m.status === ManifestStatus.READY_TO_DELIVER ||
                m.status === ManifestStatus.DELIVERING
              ).length}
            </Typography>
            <Typography variant="body2" color="warning.contrastText">
              In Progress
            </Typography>
          </Box>

          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="h4" color="info.contrastText" sx={{ fontWeight: 'bold' }}>
              {manifests.reduce((sum, m) => sum + (m.pieces || 0), 0)}
            </Typography>
            <Typography variant="body2" color="info.contrastText">
              Total Pieces
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Manifest List Section */}
      <Paper sx={{ p: 0 }}>
        <Box sx={{ p: 3, pb: 0 }}>
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <HomeIcon />
              Container Manifests
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              This view shows only the manifests belonging to container {container.containerNo}
            </Typography>

            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                size="small"
                onClick={handleOpenManifestDialog}
                sx={{
                  fontWeight: 'bold',
                  borderRadius: 2,
                  boxShadow: 2,
                  px: 3,
                  py: 1,
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                  '&:hover': {
                    boxShadow: 4,
                    bgcolor: theme.palette.primary.dark
                  },
                }}
              >
                Create Manifest
              </Button>

              <Button
                variant="contained"
                startIcon={<CloudUploadIcon />}
                size="small"
                onClick={handleOpenBulkUpload}
                sx={{
                  fontWeight: 'bold',
                  borderRadius: 2,
                  boxShadow: 2,
                  px: 3,
                  py: 1,
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                  bgcolor: theme.palette.success.main,
                  '&:hover': {
                    boxShadow: 4,
                    bgcolor: theme.palette.success.dark
                  },
                }}
              >
                Upload Manifests
              </Button>
            </Box>
          </Box>
        </Box>

        <ManifestList
          manifests={manifests}
          loading={manifestLoading}
          error={manifestError}
          containerContext={container}
          isContainerView={true}
          locationZones={locationZones}
          vehicleTypes={vehicleTypes}
          locationsLoading={locationsLoading}
          vehicleTypesLoading={vehicleTypesLoading}
          onManifestUpdate={handleManifestUpdate}
          onManifestDelete={handleManifestDelete}
          onBulkDelete={handleBulkDelete}
          onRefresh={handleRefresh}
        />
      </Paper>

      {/* Bulk Upload Dialog */}
      <Dialog
        open={bulkUploadDialogOpen}
        onClose={() => {
          if (!isUploading) {
            setBulkUploadDialogOpen(false);
            handleResetFileUpload();
          }
        }}
        maxWidth="md"
        fullWidth
        keepMounted={false}
        disableRestoreFocus={false}
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
            maxHeight: '90vh',
            overflowY: 'auto'
          },
        }}
      >
        <DialogTitle sx={{
          bgcolor: theme.palette.primary.main,
          color: 'white',
          px: 3,
          py: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Box display="flex" alignItems="center">
            <CloudUploadIcon sx={{ mr: 2 }} />
            Upload Manifests for Container {containerNo}
          </Box>
          <IconButton
            aria-label="close"
            onClick={() => {
              if (!isUploading) {
                setBulkUploadDialogOpen(false);
                handleResetFileUpload();
              }
            }}
            sx={{
              color: 'white',
            }}
            disabled={isUploading}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Stepper activeStep={activeStep} orientation="vertical">
            {/* Step 1: File Selection */}
            <Step>
              <StepLabel>Select Excel File</StepLabel>
              <StepContent>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Container-Specific Excel Template
                  </Typography>
                  <Typography variant="body2">
                    Download the formatted Excel template pre-configured for container {containerNo}.
                    Includes sample data, validation rules, and detailed instructions.
                  </Typography>
                </Alert>

                {/* Show reupload guidance if coming back from errors */}
                {activeStep === 0 && bulkUploadResults && bulkUploadResults.failed > 0 && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      🔄 Reupload Mode
                    </Typography>
                    <Typography variant="body2">
                      Fix the errors in your Excel file and upload the corrected version.
                      You can review the error details in the results section below.
                    </Typography>
                  </Alert>
                )}

                <Box sx={{ mb: 3, p: 2, borderRadius: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
                    Container-Specific Features:
                  </Typography>
                  <Typography variant="body2" component="div">
                    • Pre-configured for container: <strong>{containerNo}</strong>
                  </Typography>
                  <Typography variant="body2" component="div">
                    • Auto-assigned to client: <strong>{container?.client?.companyName || container?.client?.username || 'N/A'}</strong>
                  </Typography>
                  <Typography variant="body2" component="div">
                    • Country automatically set to Singapore
                  </Typography>
                  <Typography variant="body2" component="div">
                    • Simplified columns (no container/client selection needed)
                  </Typography>
                </Box>

                {!uploadedFile ? (
                  <Paper
                    sx={{
                      p: 4,
                      border: '2px dashed #ccc',
                      mb: 2,
                      textAlign: 'center',
                      cursor: 'pointer',
                      bgcolor: 'rgba(46, 125, 50, 0.04)',
                      '&:hover': {
                        bgcolor: 'rgba(46, 125, 50, 0.08)',
                        borderColor: 'primary.main'
                      }
                    }}
                    onClick={() => fileInputRef.current?.click()}
                    onDrop={handleFileDrop}
                    onDragOver={handleDragOver}
                  >
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".xlsx,.xls"
                      onChange={handleFileChange}
                      style={{ display: 'none' }}
                    />

                    <CloudUploadIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Drop Excel File Here or Click to Browse
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Supports .xlsx and .xls files up to 10MB
                    </Typography>
                    <Button
                      variant="outlined"
                      startIcon={<UploadFileIcon />}
                      sx={{ mt: 1 }}
                    >
                      Choose Excel File
                    </Button>
                  </Paper>
                ) : (
                  <Paper sx={{
                    p: 3,
                    mb: 2,
                    bgcolor: 'success.light',
                    color: 'success.contrastText',
                    border: '2px solid',
                    borderColor: 'success.main'
                  }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <CheckCircleIcon sx={{ fontSize: 32 }} />
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle1" fontWeight="bold">
                          ✅ File Selected: {uploadedFile.name}
                        </Typography>
                        <Typography variant="body2">
                          Size: {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB | Ready for upload
                        </Typography>
                      </Box>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={async () => {
                          console.log('🔍 Manual debug analysis...');
                          await debugExcelFile(uploadedFile);
                          toast.info('Debug analysis complete - check browser console');
                        }}
                        sx={{ color: 'inherit', borderColor: 'currentColor' }}
                      >
                        Debug File
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={handleResetFileUpload}
                        sx={{ color: 'inherit', borderColor: 'currentColor' }}
                      >
                        Change File
                      </Button>
                    </Stack>
                  </Paper>
                )}

                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Important Validation Rules:
                  </Typography>
                  <Typography variant="body2">
                    • All required fields must be filled (marked with * in template)<br/>
                    • If ANY row has validation errors, NO manifests will be created<br/>
                    • Delete sample rows before uploading your data<br/>
                    • All manifests will be assigned to container {containerNo}
                  </Typography>
                </Alert>

                {/* Navigation buttons for step 1 */}
                {uploadedFile && (
                  <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      onClick={() => setActiveStep(1)}
                      startIcon={<ArrowForwardIcon />}
                    >
                      Next: Validate & Upload
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={downloadTemplate}
                      startIcon={<UploadFileIcon />}
                    >
                      Download Template
                    </Button>
                  </Stack>
                )}
              </StepContent>
            </Step>

            {/* Step 2: Validation & Upload */}
            <Step>
              <StepLabel>Validate & Upload</StepLabel>
              <StepContent>
                <Typography variant="body1" gutterBottom>
                  File ready for processing. Click "Start Upload" to begin validation and upload.
                </Typography>

                <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                  <Button
                    variant="contained"
                    onClick={handleFileUpload}
                    disabled={isUploading}
                    startIcon={isUploading ? <CircularProgress size={20} /> : <CloudUploadIcon />}
                  >
                    {isUploading ? 'Processing...' : 'Start Upload'}
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setActiveStep(0)}
                    disabled={isUploading}
                  >
                    Back
                  </Button>
                </Stack>
              </StepContent>
            </Step>

            {/* Step 3: Results */}
            <Step>
              <StepLabel>Upload Results</StepLabel>
              <StepContent>
                {isUploading && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      Processing... {uploadProgress}%
                    </Typography>
                    <LinearProgress variant="determinate" value={uploadProgress} />
                  </Box>
                )}

                {!isUploading && bulkUploadResults && (
                  <Box sx={{ textAlign: 'center', py: 2 }}>
                    <CheckCircleIcon sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
                    <Typography variant="h6" gutterBottom>
                      Upload Complete!
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      See detailed results below the stepper.
                    </Typography>
                  </Box>
                )}

                {!isUploading && !bulkUploadResults && (
                  <Typography variant="body2" color="text.secondary">
                    Upload will begin once you click "Start Upload" in the previous step.
                  </Typography>
                )}
              </StepContent>
            </Step>
          </Stepper>

          {/* Always visible results section when upload is complete */}
          {bulkUploadResults && (
            <Box sx={{ mt: 3, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                📊 Upload Results Summary
              </Typography>

              <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
                <Paper
                  elevation={2}
                  sx={{
                    p: 2,
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2,
                    flex: 1,
                    textAlign: 'center',
                    bgcolor: 'rgba(25, 118, 210, 0.1)'
                  }}
                >
                  <Typography variant="h4" color="primary.main" fontWeight="bold">
                    {bulkUploadResults.total}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Total Processed</Typography>
                </Paper>
                <Paper
                  elevation={2}
                  sx={{
                    p: 2,
                    border: '1px solid',
                    borderColor: 'success.main',
                    borderRadius: 2,
                    flex: 1,
                    textAlign: 'center',
                    bgcolor: 'rgba(46, 125, 50, 0.1)'
                  }}
                >
                  <Typography variant="h4" color="success.main" fontWeight="bold">
                    {bulkUploadResults.successful}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Successfully Created</Typography>
                </Paper>
                <Paper
                  elevation={2}
                  sx={{
                    p: 2,
                    border: '1px solid',
                    borderColor: 'error.main',
                    borderRadius: 2,
                    flex: 1,
                    textAlign: 'center',
                    bgcolor: 'rgba(211, 47, 47, 0.1)'
                  }}
                >
                  <Typography variant="h4" color="error.main" fontWeight="bold">
                    {bulkUploadResults.failed}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Failed/Errors</Typography>
                </Paper>
              </Stack>

              {bulkUploadResults.successful > 0 && (
                <Alert severity="success" sx={{ mt: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    ✅ Successfully created {bulkUploadResults.successful} manifest(s) for container {containerNo}.
                    <strong> New manifests have been automatically added to the list above - no refresh needed!</strong>
                  </Typography>
                </Alert>
              )}

              {bulkUploadResults.failed > 0 && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    ❌ {bulkUploadResults.failed} manifest(s) failed to create.
                    Please review the error details and fix the issues in your Excel file.
                  </Typography>
                </Alert>
              )}

              {/* Enhanced Error Details Section */}
              {bulkUploadResults.errors && bulkUploadResults.errors.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" color="error.main" sx={{ fontWeight: 'bold' }}>
                      🚨 Error Details ({bulkUploadResults.errors.length})
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => setShowErrorDetails(!showErrorDetails)}
                      sx={{ borderRadius: 2 }}
                    >
                      {showErrorDetails ? 'Hide Details' : 'Show Details'}
                    </Button>
                  </Box>

                  {showErrorDetails && (
                    <Paper sx={{ p: 2, bgcolor: 'error.light', maxHeight: 300, overflowY: 'auto' }}>
                      {bulkUploadResults.errors.map((error: any, index: number) => (
                        <Alert key={index} severity="error" sx={{ mb: 1, bgcolor: 'white' }}>
                          <Typography variant="body2">
                            {error.row > 0 ? `📍 Row ${error.row}: ` : '⚠️ General Error: '}
                            <strong>{error.error}</strong>
                          </Typography>
                        </Alert>
                      ))}
                    </Paper>
                  )}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            variant="outlined"
            onClick={downloadTemplate}
            sx={{ borderRadius: 2 }}
          >
            Download Container Template
          </Button>

          {bulkUploadResults && bulkUploadResults.failed > 0 && (
            <Button
              variant="contained"
              color="warning"
              onClick={() => {
                handleResetFileUpload();
                setActiveStep(0);
              }}
              sx={{ borderRadius: 2 }}
            >
              Upload Corrected File
            </Button>
          )}

          {bulkUploadResults && bulkUploadResults.successful > 0 && bulkUploadResults.failed === 0 && (
            <Button
              variant="outlined"
              onClick={() => {
                handleResetFileUpload();
                setActiveStep(0);
              }}
              sx={{ borderRadius: 2 }}
            >
              Upload Another File
            </Button>
          )}

          <Button
            onClick={() => {
              setBulkUploadDialogOpen(false);
              handleResetFileUpload();
              setActiveStep(0);
            }}
            variant="contained"
            sx={{ borderRadius: 2 }}
          >
            {bulkUploadResults ? 'Done' : 'Cancel'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Manifest Dialog */}
      <Dialog
        open={openManifestDialog}
        onClose={handleCloseManifestDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
            maxHeight: '90vh',
            overflowY: 'auto'
          },
        }}
      >
        <DialogTitle sx={{
          bgcolor: theme.palette.primary.main,
          color: 'white',
          px: 3,
          py: 2
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Create New Manifest for Container {containerNo}
            </Typography>
            <IconButton
              aria-label="close"
              onClick={handleCloseManifestDialog}
              sx={{
                color: 'white',
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {manifestFormError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {manifestFormError}
            </Alert>
          )}

          {/* Basic Information Section */}
          <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            Basic Information
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
            <TextField
              required
              name="trackingNo"
              label="Tracking Number"
              value={manifestFormData.trackingNo}
              onChange={(e) => handleManifestFormChange('trackingNo', e.target.value)}
              fullWidth
              margin="normal"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Internal ID
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                <Box component="span" sx={{
                  color: theme.palette.primary.main,
                  display: 'inline-flex',
                  alignItems: 'center',
                  '& svg': { mr: 1 }
                }}>
                  <TimelineIcon fontSize="small" />
                </Box>
                {containerNo && manifestFormData.sequenceNo ? `${containerNo}-${manifestFormData.sequenceNo}` : 'N/A'}
              </Typography>
            </Box>
          </Box>

          {/* Client Information Section */}
          <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            Client Information
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Client
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                <Box component="span" sx={{
                  color: theme.palette.grey[600],
                  display: 'inline-flex',
                  alignItems: 'center',
                  '& svg': { mr: 1 }
                }}>
                  <PersonIcon fontSize="small" />
                </Box>
                {container?.client?.companyName ?
                  container.client.companyName :
                  container?.client?.username || 'No client selected'}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Container
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                <Box component="span" sx={{
                  color: theme.palette.grey[600],
                  display: 'inline-flex',
                  alignItems: 'center',
                  '& svg': { mr: 1 }
                }}>
                  <LocalShippingOutlinedIcon fontSize="small" />
                </Box>
                {containerNo || 'No container selected'}
              </Typography>
            </Box>
          </Box>

          {/* Package Details Section */}
          <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            Package Details
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
            <TextField
              required
              name="pieces"
              label="Pieces"
              type="number"
              value={manifestFormData.pieces || ''}
              onChange={(e) => handleManifestFormChange('pieces', Number(e.target.value))}
              fullWidth
              margin="normal"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
            <TextField
              name="cbm"
              label="CBM"
              type="number"
              value={manifestFormData.cbm || ''}
              onChange={(e) => handleManifestFormChange('cbm', Number(e.target.value))}
              fullWidth
              margin="normal"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
            <TextField
              name="weight"
              label="Weight (kg)"
              type="number"
              value={manifestFormData.weight || ''}
              onChange={(e) => handleManifestFormChange('weight', Number(e.target.value))}
              fullWidth
              margin="normal"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
            <Box sx={{ mt: 2, mb: 2 }}>
              <Typography variant="body2" color="text.secondary">Number of Pallets</Typography>
              <Typography variant="body1" sx={{ fontWeight: 500, mt: 1 }}>
                {manifestFormData.actualPalletsCount ?? 0}
                <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                  The number of pallets is automatically calculated based on created pallets
                </Typography>
              </Typography>
            </Box>
            <FormControl fullWidth margin="normal">
              <InputLabel id="location-label">Location</InputLabel>
              <Select
                labelId="location-label"
                id="location"
                name="location"
                value={manifestFormData.location || ''}
                onChange={(e) => handleManifestFormChange('location', e.target.value)}
                label="Location"
                startAdornment={
                  <InputAdornment position="start">
                    <LocationOnIcon color="action" />
                  </InputAdornment>
                }
                sx={{
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                }}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {locationZones.map((zone) => (
                  <MenuItem key={zone.id} value={zone.name}>
                    {zone.name}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Select a location zone</FormHelperText>
            </FormControl>
            <FormControl fullWidth margin="normal">
              <InputLabel id="driver-label">Driver</InputLabel>
              <Select
                labelId="driver-label"
                name="driver"
                value={manifestFormData.driver?.username || ''}
                onChange={(e) => {
                  const selectedDriver = drivers.find(d => d.username === e.target.value) || { username: '' };
                  handleManifestFormChange('driver', selectedDriver);
                }}
                label="Driver"
                MenuProps={{
                  sx: {
                    zIndex: 10001,
                    '& .MuiPaper-root': {
                      zIndex: 10001
                    }
                  }
                }}
                sx={{
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                }}
                startAdornment={
                  <InputAdornment position="start">
                    <PersonIcon color="action" />
                  </InputAdornment>
                }
              >
                <MenuItem value="">
                  <em>Not Assigned</em>
                </MenuItem>
                {drivers && drivers.length > 0 ? (
                  drivers.map((driver) => (
                    <MenuItem key={driver.username} value={driver.username}>
                      {`${driver.username} - ${driver.fullName || ''}`.trim()}
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem disabled value="">
                    No drivers available
                  </MenuItem>
                )}
              </Select>
              <FormHelperText>Driver selection is optional</FormHelperText>
            </FormControl>
            <FormControl fullWidth margin="normal">
              <InputLabel id="delivery-vehicle-label">Delivery Vehicle</InputLabel>
              <Select
                labelId="delivery-vehicle-label"
                id="deliveryVehicle"
                name="deliveryVehicle"
                value={vehicleTypes.some(vt => vt.name === manifestFormData.deliveryVehicle) ? manifestFormData.deliveryVehicle : ''}
                onChange={(e) => handleManifestFormChange('deliveryVehicle', e.target.value)}
                label="Delivery Vehicle"
                MenuProps={{
                  sx: {
                    zIndex: 10001,
                    '& .MuiPaper-root': {
                      zIndex: 10001
                    }
                  }
                }}
                startAdornment={
                  <InputAdornment position="start">
                    <LocalShippingOutlinedIcon color="action" />
                  </InputAdornment>
                }
                sx={{
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                }}
              >
                <MenuItem value="">
                  <em>Auto-assign</em>
                </MenuItem>
                {vehicleTypes.map((vehicleType) => (
                  <MenuItem key={vehicleType.id} value={vehicleType.name}>
                    {vehicleType.name}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText sx={{ display: 'flex', alignItems: 'center' }}>
                Will be auto-assigned based on weight, CBM, and pieces if left empty
                <Tooltip title="The system will automatically determine the appropriate vehicle type based on the manifest's weight, CBM, and number of pieces. You can manually select a different vehicle type if needed." arrow>
                  <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
                    <InfoIcon fontSize="small" color="action" />
                  </IconButton>
                </Tooltip>
              </FormHelperText>
            </FormControl>
          </Box>

          {/* Shipping Information Section */}
          <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            Shipping Information
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
            <TextField
              required
              name="customerName"
              label="Customer Name"
              value={manifestFormData.customerName}
              onChange={(e) => handleManifestFormChange('customerName', e.target.value)}
              fullWidth
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
            <TextField
              required
              name="phoneNo"
              label="Phone Number"
              value={manifestFormData.phoneNo}
              onChange={(e) => handleManifestFormChange('phoneNo', e.target.value)}
              fullWidth
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
            <TextField
              required
              name="address"
              label="Address"
              value={manifestFormData.address}
              onChange={(e) => handleManifestFormChange('address', e.target.value)}
              fullWidth
              margin="normal"
              sx={{
                gridColumn: '1 / -1',
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <HomeIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              required
              name="postalCode"
              label="Postal Code"
              value={manifestFormData.postalCode || ''}
              onChange={(e) => handleManifestFormChange('postalCode', e.target.value)}
              fullWidth
              margin="normal"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
            <TextField
              required
              name="country"
              label="Country"
              value={manifestFormData.country || ''}
              onChange={(e) => handleManifestFormChange('country', e.target.value)}
              fullWidth
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationOnIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
          </Box>

          {/* Delivery Information Section */}
          <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            Delivery Information
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2, mb: 3 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Box position="relative" zIndex={1300} width="100%">
                <SplitDeliveryDatePicker
                  label="Delivery Date & Time Slot"
                  value={manifestFormData.deliveryDate}
                  onChange={(newValue) => handleManifestFormChange('deliveryDate', newValue)}
                  timeSlotValue={manifestFormData.timeSlot}
                  onTimeSlotChange={(timeSlot) => handleManifestFormChange('timeSlot', timeSlot)}
                  fullWidth
                  slotProps={{
                    datePicker: {
                      popper: {
                        sx: { zIndex: 9999 }
                      }
                    }
                  }}
                />
              </Box>
            </LocalizationProvider>
          </Box>

          {/* Driver Remarks Section */}
          <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            Driver Remarks
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2, mb: 3 }}>
            <TextField
              name="driverRemarks"
              label="Driver Remarks"
              value={manifestFormData.driverRemarks || ''}
              onChange={(e) => handleManifestFormChange('driverRemarks', e.target.value)}
              multiline
              rows={3}
              fullWidth
              margin="normal"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
          </Box>

          {/* Remarks (CS) Section */}
          <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
            Remarks (CS)
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2, mb: 3 }}>
            <TextField
              name="remarks"
              label="Remarks (CS)"
              value={manifestFormData.remarks || ''}
              onChange={(e) => handleManifestFormChange('remarks', e.target.value)}
              multiline
              rows={4}
              fullWidth
              margin="normal"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main,
                  },
                },
              }}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={handleCloseManifestDialog} variant="outlined">Cancel</Button>
          <Button onClick={handleSaveManifest} variant="contained" color="primary" disabled={manifestFormLoading}>
            {manifestFormLoading ? <CircularProgress size={24} /> : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Container Status Update Dialog */}
      <Dialog
        open={statusDialogOpen}
        onClose={() => setStatusDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24
          },
        }}
      >
        <DialogTitle sx={{
          bgcolor: 'primary.main',
          color: 'primary.contrastText',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Box display="flex" alignItems="center">
            <LocalShippingOutlinedIcon sx={{ mr: 1 }} />
            Update Container Status
          </Box>
          {container && (
            <Chip
              label={container.status}
              size="small"
              icon={getContainerStatusIcon(container.status)}
              sx={{
                ...getStatusChipColor(container.status),
                fontWeight: 'medium',
                fontSize: '0.75rem',
                backgroundColor: 'grey.300'
              }}
            />
          )}
        </DialogTitle>

        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Container: {container?.containerNo}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Select the new status for this container:
            </Typography>
            <Typography variant="caption" color="error">
              Note: Changing container status may affect all manifests in this container
            </Typography>
          </Box>

          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
            gap: 2
          }}>
            {Object.values(ContainerStatus).map((status) => {
              const isSelected = newStatus === status;
              const isCurrentStatus = container?.status === status;
              const statusColors = getStatusChipColor(status);

              return (
                <Paper
                  key={status}
                  elevation={isSelected ? 3 : 1}
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    border: isSelected ? `1px solid ${theme.palette.primary.main}` : '1px solid transparent',
                    cursor: isCurrentStatus ? 'default' : 'pointer',
                    backgroundColor: isSelected ? alpha(statusColors.bg, 0.5) : 'background.paper',
                    opacity: isCurrentStatus ? 0.7 : 1,
                    transition: 'all 0.2s',
                    '&:hover': {
                      backgroundColor: isCurrentStatus ? 'inherit' : alpha(statusColors.bg, 0.3),
                      transform: isCurrentStatus ? 'none' : 'translateY(-2px)',
                      boxShadow: isCurrentStatus ? 1 : 3
                    }
                  }}
                  onClick={() => !isCurrentStatus && setNewStatus(status)}
                >
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {getContainerStatusIcon(status)}
                      <Typography sx={{ ml: 1, fontWeight: isSelected ? 'bold' : 'normal' }}>
                        {status.replace(/_/g, ' ')}
                      </Typography>
                    </Box>
                    {isCurrentStatus && (
                      <Chip label="Current" size="small" color="primary" />
                    )}
                  </Box>

                  {isSelected && (
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      {isCurrentStatus
                        ? "This is the current status"
                        : `Change status from ${container?.status.replace(/_/g, ' ')} to ${status.replace(/_/g, ' ')}`
                      }
                    </Typography>
                  )}
                </Paper>
              );
            })}
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSaveStatus}
            variant="contained"
            color="primary"
            disabled={!newStatus || (container && newStatus === container.status)}
            startIcon={<CheckCircleIcon />}
          >
            Update Status
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContainerDetailTest;
