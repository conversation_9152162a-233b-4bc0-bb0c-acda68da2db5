import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Alert,
  Fab,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';
import containerService from '../../services/container.service';
import userService from '../../services/user.service';
import { Container, ContainerStatus } from '../../types/Container';
import { Client } from '../../types/User';
import ContainerListToolbar from './ContainerListToolbar';
import ContainerListFilters from './ContainerListFilters';
import ContainerListGrid from './ContainerListGrid';
import { ContainerListFilters as FiltersType, ContainerListTotals, ContainerListLoadingStates } from './types';

// Props interface
interface ContainerListProps {
  onContainerDelete?: (containerNo: string) => void;
  onBulkDelete?: (containerNos: string[]) => void;
  onCreateContainer?: () => void;
  onUploadContainer?: () => void;
}

/**
 * ContainerList Component
 * 
 * Main container management component that provides:
 * - Container listing with advanced filtering
 * - Real-time updates and data management
 * - Bulk operations and selection
 * - Professional UI following ManifestList patterns
 */
const ContainerList: React.FC<ContainerListProps> = ({
  onContainerDelete,
  onBulkDelete,
  onCreateContainer,
  onUploadContainer
}) => {
  const theme = useTheme();
  const { currentUser } = useAuth();
  const toast = useToast();
  
  // Refs
  const containerListRef = useRef<HTMLDivElement>(null);
  const containerTitleRef = useRef<HTMLDivElement>(null);
  
  // State management
  const [containers, setContainers] = useState<Container[]>([]);
  const [filteredContainers, setFilteredContainers] = useState<Container[]>([]);
  const [selectedContainers, setSelectedContainers] = useState<Container[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [clients, setClients] = useState<Client[]>([]);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  
  // Filter state
  const [filters, setFilters] = useState<FiltersType>({
    search: '',
    status: [],
    dateRange: {
      start: null,
      end: null
    },
    dateFieldType: 'etaRequestedDate',
    client: '',
    truckNo: '',
    vesselVoyage: '',
    loadingBay: ''
  });
  
  // Loading states for resources
  const [loadingStates, setLoadingStates] = useState<ContainerListLoadingStates>({
    clientsLoading: false
  });
  
  // Load containers from API
  const loadContainers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await containerService.getAllContainers(currentUser?.token || '');
      if (response.success && response.data) {
        const containerData = Array.isArray(response.data) ? response.data : [];
        setContainers(containerData);
        console.log(`Loaded ${containerData.length} containers`);
      } else {
        setError(response.message || 'Failed to load containers');
        setContainers([]); // Ensure containers is always an array
      }
    } catch (error: any) {
      console.error('Error loading containers:', error);
      setError(error.response?.data?.message || 'Failed to load containers');
      setContainers([]); // Ensure containers is always an array even on error
    } finally {
      setLoading(false);
    }
  }, [currentUser?.token]);
  
  // Load clients
  const loadClients = useCallback(async () => {
    try {
      setLoadingStates(prev => ({ ...prev, clientsLoading: true }));

      const response = await userService.getClients(currentUser?.token || '');
      if (response.success && response.data) {
        setClients(response.data);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
    } finally {
      setLoadingStates(prev => ({ ...prev, clientsLoading: false }));
    }
  }, [currentUser?.token]);
  
  // Filter containers based on current filters
  const applyFilters = useCallback(() => {
    let filtered = [...containers];
    
    // Search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase().trim();
      filtered = filtered.filter(container =>
        container.containerNo?.toLowerCase().includes(searchTerm) ||
        container.client?.companyName?.toLowerCase().includes(searchTerm) ||
        container.client?.username?.toLowerCase().includes(searchTerm) ||
        container.truckNo?.toLowerCase().includes(searchTerm) ||
        container.vesselVoyageNo?.toLowerCase().includes(searchTerm) ||
        container.loadingBay?.toLowerCase().includes(searchTerm) ||
        container.unstuffTeam?.toLowerCase().includes(searchTerm) ||
        container.remark?.toLowerCase().includes(searchTerm)
      );
    }
    
    // Status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(container =>
        filters.status.includes(container.status)
      );
    }
    
    // Client filter
    if (filters.client.trim()) {
      filtered = filtered.filter(container =>
        container.client?.companyName?.toLowerCase().includes(filters.client.toLowerCase()) ||
        container.client?.username?.toLowerCase().includes(filters.client.toLowerCase())
      );
    }
    
    // Truck number filter
    if (filters.truckNo.trim()) {
      filtered = filtered.filter(container =>
        container.truckNo?.toLowerCase().includes(filters.truckNo.toLowerCase())
      );
    }
    
    // Vessel/Voyage filter
    if (filters.vesselVoyage.trim()) {
      filtered = filtered.filter(container =>
        container.vesselVoyageNo?.toLowerCase().includes(filters.vesselVoyage.toLowerCase())
      );
    }
    
    // Loading bay filter
    if (filters.loadingBay.trim()) {
      filtered = filtered.filter(container =>
        container.loadingBay?.toLowerCase().includes(filters.loadingBay.toLowerCase())
      );
    }
    
    // Date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(container => {
        const dateField = filters.dateFieldType;
        let containerDate: string | undefined;

        // Safe field access
        switch (dateField) {
          case 'etaRequestedDate':
            containerDate = container.etaRequestedDate;
            break;
          case 'etaAllocated':
            containerDate = container.etaAllocated;
            break;
          case 'arrivalDate':
            containerDate = container.arrivalDate;
            break;
          case 'unstuffDate':
            containerDate = container.unstuffDate;
            break;
          default:
            containerDate = container.etaRequestedDate;
        }

        if (!containerDate) return false;

        const date = new Date(containerDate);
        const startDate = filters.dateRange.start;
        const endDate = filters.dateRange.end;

        if (startDate && date < startDate) return false;
        if (endDate && date > endDate) return false;

        return true;
      });
    }
    
    setFilteredContainers(filtered);
  }, [containers, filters]);
  
  // Handle filter changes
  const handleFilterChange = useCallback((filterKey: keyof FiltersType, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }));
  }, []);
  
  // Reset all filters
  const handleResetFilters = useCallback(() => {
    setFilters({
      search: '',
      status: [],
      dateRange: {
        start: null,
        end: null
      },
      dateFieldType: 'etaRequestedDate',
      client: '',
      truckNo: '',
      vesselVoyage: '',
      loadingBay: ''
    });
  }, []);
  
  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadContainers();
    loadClients();
  }, [loadContainers, loadClients]);
  
  // Scroll to top functionality
  const handleScrollToTop = useCallback(() => {
    if (containerTitleRef.current) {
      const rect = containerTitleRef.current.getBoundingClientRect();
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      const targetY = rect.top + scrollTop - 100;

      if ('scrollBehavior' in document.documentElement.style) {
        window.scrollTo({
          top: targetY,
          behavior: 'smooth'
        });
      } else {
        const startY = window.scrollY;
        const distance = targetY - startY;
        const duration = 500;
        let startTime: number | null = null;

        const animateScroll = (currentTime: number) => {
          if (startTime === null) startTime = currentTime;
          const timeElapsed = currentTime - startTime;
          const progress = Math.min(timeElapsed / duration, 1);
          const ease = progress * (2 - progress);
          window.scrollTo(0, startY + (distance * ease));

          if (progress < 1) {
            requestAnimationFrame(animateScroll);
          }
        };

        requestAnimationFrame(animateScroll);
      }
    } else if (containerListRef.current) {
      const rect = containerListRef.current.getBoundingClientRect();
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      const targetY = rect.top + scrollTop - 100;

      if ('scrollBehavior' in document.documentElement.style) {
        window.scrollTo({
          top: targetY,
          behavior: 'smooth'
        });
      } else {
        window.scrollTo(0, targetY);
      }
    } else {
      if ('scrollBehavior' in document.documentElement.style) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } else {
        window.scrollTo(0, 0);
      }
    }
  }, []);
  
  // Scroll detection for back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollToTop(window.scrollY > 300);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Apply filters when containers or filters change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);
  
  // Load data on mount
  useEffect(() => {
    loadContainers();
    loadClients();
  }, [loadContainers, loadClients]);
  
  // Real-time event listeners
  useEffect(() => {
    const handleContainerCreated = () => {
      loadContainers();
    };
    
    const handleContainerUpdated = () => {
      loadContainers();
    };
    
    const handleContainerDeleted = () => {
      loadContainers();
    };
    
    const handleBulkDeleted = () => {
      loadContainers();
    };
    
    window.addEventListener('container-created', handleContainerCreated);
    window.addEventListener('container-updated', handleContainerUpdated);
    window.addEventListener('container-deleted', handleContainerDeleted);
    window.addEventListener('containers-bulk-deleted', handleBulkDeleted);
    
    return () => {
      window.removeEventListener('container-created', handleContainerCreated);
      window.removeEventListener('container-updated', handleContainerUpdated);
      window.removeEventListener('container-deleted', handleContainerDeleted);
      window.removeEventListener('containers-bulk-deleted', handleBulkDeleted);
    };
  }, []);
  
  // Memoized totals for performance
  const totals: ContainerListTotals = useMemo(() => ({
    total: containers.length,
    filtered: filteredContainers.length,
    selected: selectedContainers.length,
    byStatus: containers.reduce((acc, container) => {
      acc[container.status] = (acc[container.status] || 0) + 1;
      return acc;
    }, {} as Record<ContainerStatus, number>)
  }), [containers, filteredContainers, selectedContainers]);

  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '400px',
        width: '100%'
      }}>
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          Loading containers...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box 
      ref={containerListRef}
      sx={{ 
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative'
      }}
    >
      {/* Toolbar with action buttons */}
      <ContainerListToolbar
        ref={containerTitleRef}
        selectedContainers={selectedContainers}
        totalContainers={filteredContainers.length}
        onRefresh={handleRefresh}
        onCreateContainer={onCreateContainer}
        onUploadContainer={onUploadContainer}
        onDelete={onBulkDelete}
      />

      {/* Filters section */}
      <ContainerListFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        onResetFilters={handleResetFilters}
        clients={clients}
        loading={loadingStates}
        totals={totals}
      />

      {/* Main data grid */}
      <Box sx={{ flexGrow: 1, width: '100%' }}>
        <ContainerListGrid
          containers={filteredContainers}
          selectedContainers={selectedContainers}
          onSelectionChange={setSelectedContainers}
          onContainerDelete={onContainerDelete}
          loading={loading}
        />
      </Box>

      {/* Back to top button */}
      {showScrollToTop && (
        <Fab
          color="primary"
          size="medium"
          onClick={handleScrollToTop}
          aria-label="scroll to above Container List title"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1000,
            boxShadow: theme.shadows[8],
            '&:hover': {
              boxShadow: theme.shadows[12],
              transform: 'scale(1.05)',
            },
            transition: 'all 0.2s ease-in-out',
            backgroundColor: alpha(theme.palette.primary.main, 0.9),
            '&:hover': {
              backgroundColor: theme.palette.primary.main,
            }
          }}
        >
          <Tooltip title="Back to Container List" placement="left">
            <KeyboardArrowUpIcon />
          </Tooltip>
        </Fab>
      )}
    </Box>
  );
};

export default ContainerList;
