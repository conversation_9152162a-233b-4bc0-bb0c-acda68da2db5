import React, { useEffect, useState, useRef, useCallback, Suspense } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Button,
  <PERSON>ack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  LinearProgress,
  Divider,
  Paper,
  IconButton,
  Chip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Collapse,
  Autocomplete,
  InputAdornment,
  FormHelperText,
  SelectChangeEvent,
  Tooltip
} from '@mui/material';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import TimelineIcon from '@mui/icons-material/Timeline';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import PhoneIcon from '@mui/icons-material/Phone';
import HomeIcon from '@mui/icons-material/Home';
import InfoIcon from '@mui/icons-material/Info';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import GetAppIcon from '@mui/icons-material/GetApp';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import AddIcon from '@mui/icons-material/Add';
import ManifestList from '../components/ManifestList';
import usePageTitle from '../hooks/usePageTitle';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../contexts/ToastContext';
import SplitDeliveryDatePicker from '../components/SplitDeliveryDatePicker';
import manifestService from '../services/manifest.service';
import containerService from '../services/container.service';
import userService from '../services/user.service';
import locationService from '../services/location.service';
import * as vehicleService from '../services/vehicle.service';
import { Manifest, ManifestStatus } from '../types/manifest';
import { Client, Driver } from '../types/User';
import { Container } from '../types/Container';
import { LocationZone } from '../types/location';
import { VehicleType } from '../types/Vehicle';
import Excel from 'exceljs';

// Debounce helper function
const debounce = (fn: Function, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function(...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), ms);
  };
};

// Loading fallback component
const LoadingFallback = () => (
  <Box sx={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center', 
    height: '300px',
    width: '100%'
  }}>
    <CircularProgress size={40} />
    <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
      Loading manifest list...
    </Typography>
  </Box>
);

/**
 * Main Manifest Management page with enhanced functionality
 * Includes complete manifest list, bulk upload, and individual manifest creation
 * Replaces the original ManifestManagement page with improved features
 */
const ManifestListTest: React.FC = () => {
  // Set the page title
  usePageTitle('Manifest Management');

  // Auth and toast contexts
  const { currentUser } = useAuth();
  const toast = useToast();

  // State to track container width for display purposes only
  const [containerWidth, setContainerWidth] = useState<number>(0);

  // Reference to measure width
  const containerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // ===== BULK UPLOAD STATE =====
  const [bulkUploadDialogOpen, setBulkUploadDialogOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [bulkUploadResults, setBulkUploadResults] = useState<{
    total: number;
    successful: number;
    failed: number;
    errors: Array<{ row: number; error: string }>;
  } | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [showErrorDetails, setShowErrorDetails] = useState(false);

  // Resource data for validation
  const [clients, setClients] = useState<Client[]>([]);
  const [containers, setContainers] = useState<Container[]>([]);
  const [resourcesLoading, setResourcesLoading] = useState(false);

  // ===== CREATE MANIFEST STATE =====
  const [createManifestDialogOpen, setCreateManifestDialogOpen] = useState(false);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [locationZones, setLocationZones] = useState<LocationZone[]>([]);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleType[]>([]);
  const [locationsLoading, setLocationsLoading] = useState(false);
  const [vehicleTypesLoading, setVehicleTypesLoading] = useState(false);
  const [formData, setFormData] = useState({
    trackingNo: '',
    client: { username: '' },
    container: { containerNo: '' },
    driver: { username: '' },
    sequenceNo: 0,
    status: ManifestStatus.CREATED,
    customerName: '',
    phoneNo: '',
    address: '',
    postalCode: '',
    country: '',
    pieces: 0,
    cbm: 0,
    actualPalletsCount: 0,
    weight: 0,
    location: '',
    deliveryDate: null as Date | null,
    timeSlot: '',
    deliveredDate: null as Date | null,
    deliveryVehicle: '',
    driverRemarks: '',
    remarks: '',
    internalId: '',
    createdDate: null as Date | null,
  });
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState('');

  // Ref for the first input field to manage focus
  const firstInputRef = useRef<HTMLInputElement>(null);

  // Create debounced update function
  const debouncedUpdateWidth = useCallback(
    debounce(() => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    }, 250),
    []
  );

  // ===== MANIFEST MANAGEMENT FUNCTIONS =====

  // Handle manifest deletion
  const handleManifestDelete = async (trackingNo: string) => {
    try {
      if (!currentUser?.token) {
        throw new Error('Authentication token not available');
      }

      const response = await manifestService.deleteManifest(trackingNo, currentUser.token);

      if (response.success) {
        toast.success(`Manifest ${trackingNo} deleted successfully`);

        // Emit event to refresh the manifest list
        const manifestDeletedEvent = new CustomEvent('manifest-deleted', {
          detail: {
            type: 'deleted',
            trackingNo: trackingNo,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(manifestDeletedEvent);
        console.log(`📡 Emitted manifest deletion event: ${trackingNo}`);
      } else {
        throw new Error(response.message || 'Failed to delete manifest');
      }
    } catch (error: any) {
      console.error('Error deleting manifest:', error);
      toast.error(error.message || 'Failed to delete manifest');
      throw error; // Re-throw to let the dialog handle the error state
    }
  };

  // Handle bulk manifest deletion
  const handleBulkManifestDelete = async (trackingNos: string[]) => {
    try {
      if (!currentUser?.token) {
        throw new Error('Authentication token not available');
      }

      // Delete manifests one by one
      const promises = trackingNos.map(trackingNo =>
        manifestService.deleteManifest(trackingNo, currentUser.token!)
      );

      const results = await Promise.allSettled(promises);

      // Count successful deletions
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.length - successful;

      if (successful > 0) {
        toast.success(`Successfully deleted ${successful} manifest(s)`);

        // Emit event to refresh the manifest list
        const manifestBulkDeletedEvent = new CustomEvent('manifests-bulk-deleted', {
          detail: {
            type: 'bulk_deleted',
            trackingNos: trackingNos,
            successful: successful,
            failed: failed,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(manifestBulkDeletedEvent);
        console.log(`📡 Emitted bulk manifest deletion event: ${successful} deleted, ${failed} failed`);
      }

      if (failed > 0) {
        toast.error(`Failed to delete ${failed} manifest(s)`);
      }
    } catch (error: any) {
      console.error('Error in bulk delete:', error);
      toast.error(error.message || 'Failed to delete manifests');
      throw error; // Re-throw to let the dialog handle the error state
    }
  };

  // ===== BULK UPLOAD FUNCTIONS =====

  // Load resources for validation
  const loadResources = async (): Promise<{ clients: Client[], containers: Container[] }> => {
    if (!currentUser?.token) return { clients: [], containers: [] };

    setResourcesLoading(true);
    try {
      const [clientsResponse, containersResponse] = await Promise.all([
        userService.getClients(currentUser.token),
        containerService.getAllContainers(currentUser.token)
      ]);

      const loadedClients = clientsResponse.success ? clientsResponse.data : [];
      const loadedContainers = containersResponse.success ? containersResponse.data : [];

      setClients(loadedClients);
      setContainers(loadedContainers);

      return { clients: loadedClients, containers: loadedContainers };
    } catch (error) {
      console.error('Error loading resources:', error);
      toast.error('Failed to load validation resources');
      return { clients: [], containers: [] };
    } finally {
      setResourcesLoading(false);
    }
  };

  // Open bulk upload dialog
  const handleOpenBulkUpload = () => {
    setBulkUploadDialogOpen(true);
    setActiveStep(0);
    setBulkUploadResults(null);
    setUploadedFile(null);
    setUploadProgress(0);
    // Load resources in background, but don't wait for it here
    loadResources().catch(error => {
      console.error('Error preloading resources:', error);
    });
  };

  // Enhanced file selection with automatic debugging
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;

    if (file) {
      // Check file type - Excel only for better consistency
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast.error('Please upload an Excel file (.xlsx or .xls)');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      setUploadedFile(file);
      // Don't auto-advance to next step - let user see the selected file and proceed manually
      toast.success(`✅ File "${file.name}" selected successfully! Review the file details and click "Next" when ready to proceed.`);

      // Automatically run debug analysis when file is selected
      console.log('🔍 Auto-analyzing selected Excel file...');
      try {
        await debugExcelFile(file);
      } catch (error) {
        console.error('Auto-debug failed:', error);
      }
    } else {
      setUploadedFile(null);
      setActiveStep(0);
    }
  };

  // Handle file drop (drag & drop support)
  const handleFileDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];

      // Check file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast.error('Please upload an Excel file (.xlsx or .xls)');
        return;
      }

      // Check file size
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit');
        return;
      }

      setUploadedFile(file);
      // Don't auto-advance to next step - let user see the selected file and proceed manually
      toast.success(`✅ File "${file.name}" selected successfully! Review the file details and click "Next" when ready to proceed.`);
    }
  };

  // Prevent default behavior for drag over
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  // Reset file upload state
  const handleResetFileUpload = () => {
    setUploadedFile(null);
    setUploadProgress(0);
    setBulkUploadResults(null);
    setActiveStep(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Helper function to parse rows array data
  const parseRowsArrayData = (allRows: any[]): any[] => {
    console.log('🔄 Starting row array parsing...');
    const rows: any[] = [];
    let headerRowIndex = -1;

    // Find header row (look for "Tracking" in first column)
    for (let i = 1; i < Math.min(allRows.length, 5); i++) {
      const row = allRows[i];
      if (row && Array.isArray(row) && row.length > 0) {
        const firstCell = row[1]?.toString().toLowerCase() || '';
        console.log(`🔍 Checking row ${i} for headers: "${firstCell}"`);
        if (firstCell.includes('tracking') || firstCell.includes('track')) {
          headerRowIndex = i;
          console.log(`✅ Found header row at index ${i}`);
          break;
        }
      }
    }

    if (headerRowIndex === -1) {
      console.warn('⚠️ No header row found, assuming row 1 is headers');
      headerRowIndex = 1;
    }

    // Process data rows (start after header)
    for (let i = headerRowIndex + 1; i < allRows.length; i++) {
      const row = allRows[i];

      if (!row || !Array.isArray(row)) {
        console.log(`⏭️ Skipping row ${i} - not an array`);
        continue;
      }

      // Get tracking number (first column, index 1 in ExcelJS)
      const trackingNo = row[1]?.toString().trim() || '';

      if (!trackingNo) {
        console.log(`⏭️ Skipping row ${i} - empty tracking number`);
        continue;
      }

      // Skip sample data
      if (trackingNo === 'TRK12345' || trackingNo.toLowerCase().includes('instruction')) {
        console.log(`⏭️ Skipping sample/instruction row ${i}: ${trackingNo}`);
        continue;
      }

      console.log(`✅ Processing data row ${i} with tracking: ${trackingNo}`);

      // Helper function to safely extract string value from Excel array cell
      const getCellStringValue = (cellIndex: number, fieldName: string): string => {
        try {
          const cellValue = row[cellIndex];

          // Handle null/undefined
          if (cellValue === null || cellValue === undefined) {
            return '';
          }

          // Handle string values
          if (typeof cellValue === 'string') {
            return cellValue.trim();
          }

          // Handle number values
          if (typeof cellValue === 'number') {
            return cellValue.toString().trim();
          }

          // Handle rich text objects (common in Excel)
          if (typeof cellValue === 'object' && cellValue.richText) {
            // Extract text from rich text format
            return cellValue.richText.map((rt: any) => rt.text || '').join('').trim();
          }

          // Handle other object types
          if (typeof cellValue === 'object') {
            console.warn(`Row ${i}: ${fieldName} contains object data:`, cellValue);

            // Try to extract text property if it exists
            if (cellValue.text) {
              return cellValue.text.toString().trim();
            }

            // Try to extract result property if it exists (for formulas)
            if (cellValue.result !== undefined) {
              return cellValue.result.toString().trim();
            }

            throw new Error(`${fieldName} contains invalid data format. Expected text, got object. Please ensure the cell contains plain text.`);
          }

          // Fallback to string conversion
          return cellValue.toString().trim();
        } catch (error) {
          console.error(`Row ${i}: Error processing ${fieldName}:`, error);
          throw new Error(`${fieldName} contains invalid data format. Please check the cell content.`);
        }
      };

      // Helper function to safely extract numeric value from Excel array cell
      const getCellNumericValue = (cellIndex: number, fieldName: string): number => {
        try {
          const cellValue = row[cellIndex];

          // Handle null/undefined
          if (cellValue === null || cellValue === undefined) {
            return 0;
          }

          // Handle number values
          if (typeof cellValue === 'number') {
            return cellValue;
          }

          // Handle string values
          if (typeof cellValue === 'string') {
            const trimmed = cellValue.trim();
            if (trimmed === '') return 0;
            const parsed = parseFloat(trimmed);
            return isNaN(parsed) ? 0 : parsed;
          }

          // Handle object types
          if (typeof cellValue === 'object') {
            console.warn(`Row ${i}: ${fieldName} contains object data:`, cellValue);

            // Try to extract numeric properties
            if (cellValue.result !== undefined) {
              const parsed = parseFloat(cellValue.result.toString());
              return isNaN(parsed) ? 0 : parsed;
            }

            if (cellValue.text !== undefined) {
              const parsed = parseFloat(cellValue.text.toString());
              return isNaN(parsed) ? 0 : parsed;
            }

            console.warn(`Row ${i}: ${fieldName} object cannot be converted to number, defaulting to 0`);
            return 0;
          }

          // Fallback conversion
          const parsed = parseFloat(cellValue.toString());
          return isNaN(parsed) ? 0 : parsed;
        } catch (error) {
          console.error(`Row ${i}: Error processing ${fieldName}:`, error);
          return 0;
        }
      };

      // Map the row data to our structure (reordered to match template)
      const rowData = {
        trackingNo: getCellStringValue(1, 'Tracking'),
        customerName: getCellStringValue(2, 'Cust'),
        address: getCellStringValue(3, 'Address'),
        postalCode: getCellStringValue(4, 'Postal Code'),
        phoneNo: getCellStringValue(5, 'Phone'),
        pieces: getCellNumericValue(6, 'Pcs'),
        cbm: getCellNumericValue(7, 'CBM'),
        driverRemarks: getCellStringValue(8, 'For Driver Only'),
        remarks: getCellStringValue(9, 'For CS Only'),
        weight: getCellNumericValue(10, 'Weight'),
        containerNo: getCellStringValue(11, 'Container'),
        clientUsername: getCellStringValue(12, 'Client')
      };

      console.log(`📝 Row ${i} data:`, rowData);

      // Add to results if we have a tracking number
      if (rowData.trackingNo) {
        rows.push(rowData);
        console.log(`✅ Added row ${i} to results`);
      } else {
        console.log(`❌ Skipped row ${i} - no tracking number after processing`);
      }
    }

    console.log(`🎉 Row parsing complete: ${rows.length} valid rows found`);
    return rows;
  };

  // Simplified and more robust Excel parsing
  const parseExcelFile = async (file: File): Promise<any[]> => {
    console.log('🔍 Starting Excel file parsing for:', file.name);
    console.log('📁 File details:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified).toISOString()
    });

    try {
      console.log('📖 Reading file as array buffer...');
      const arrayBuffer = await file.arrayBuffer();
      console.log('✅ Array buffer created, size:', arrayBuffer.byteLength);

      console.log('📊 Loading Excel workbook...');
      const workbook = new Excel.Workbook();
      await workbook.xlsx.load(arrayBuffer);
      console.log('✅ Workbook loaded successfully');

      console.log('📋 Getting first worksheet...');
      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) {
        console.error('❌ No worksheet found in Excel file');
        throw new Error('No worksheet found in Excel file');
      }

      console.log('📊 Worksheet loaded successfully');
      console.log('📏 Worksheet dimensions:', {
        name: worksheet.name,
        rowCount: worksheet.rowCount,
        actualRowCount: worksheet.actualRowCount,
        columnCount: worksheet.columnCount,
        actualColumnCount: worksheet.actualColumnCount
      });

      console.log('📋 Getting sheet values...');
      // Get all rows as values (this is more reliable than eachRow)
      const allRows = worksheet.getSheetValues();
      console.log('📋 Raw sheet values type:', typeof allRows);
      console.log('📋 Raw sheet values:', allRows);
      console.log('📋 Total rows found:', allRows ? allRows.length : 0);

      if (!allRows) {
        console.error('❌ getSheetValues() returned null/undefined');
        console.log('🔄 Trying alternative method...');

        // Try alternative method using eachRow
        const alternativeRows: any[] = [];
        worksheet.eachRow((row, rowNumber) => {
          const rowValues: any[] = [];
          row.eachCell((cell, colNumber) => {
            rowValues[colNumber] = cell.value;
          });
          alternativeRows[rowNumber] = rowValues;
        });

        console.log('📋 Alternative method results:', alternativeRows);
        if (alternativeRows.length < 2) {
          console.error('❌ Alternative method also found no data');
          return [];
        }

        // Use alternative results
        console.log('✅ Using alternative parsing method');
        return parseRowsArrayData(alternativeRows);
      }

      if (allRows.length < 2) {
        console.error('❌ Not enough rows found in worksheet');
        console.log('📊 Available rows:', allRows.length);
        console.log('📋 All rows content:', allRows);
        return [];
      }

      // Use the helper function to parse the rows
      return parseRowsArrayData(allRows);

    } catch (error: any) {
      console.error('💥 Excel parsing error:', error);
      throw new Error(`Failed to parse Excel file: ${error?.message || 'Unknown error'}`);
    }
  };

  // Debug function that users can call from console
  const debugExcelFile = async (file: File) => {
    console.log('🔧 DEBUG MODE: Analyzing Excel file structure');

    try {
      const workbook = new Excel.Workbook();
      const arrayBuffer = await file.arrayBuffer();
      await workbook.xlsx.load(arrayBuffer);

      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) {
        console.error('No worksheet found');
        return null;
      }

      console.log('📊 Worksheet info:', {
        name: worksheet.name,
        rowCount: worksheet.rowCount,
        actualRowCount: worksheet.actualRowCount,
        columnCount: worksheet.columnCount
      });

      // Get raw values
      const allRows = worksheet.getSheetValues();
      console.log('📋 All rows (first 10):');
      for (let i = 1; i <= Math.min(10, allRows?.length || 0); i++) {
        console.log(`Row ${i}:`, allRows[i]);
      }

      return allRows;
    } catch (error) {
      console.error('Debug failed:', error);
    }
  };

  // Make debug function available globally for testing
  (window as any).debugExcelFile = debugExcelFile;

  // Debug function to check vehicle type configuration
  const checkVehicleTypes = async () => {
    try {
      console.log('🔍 Checking vehicle type configuration...');

      // This would need to be implemented - checking if there's a vehicle type service
      // For now, just provide guidance
      console.log('💡 To check vehicle types:');
      console.log('1. Go to Vehicle Management in the admin panel');
      console.log('2. Check if vehicle types are configured with weight/CBM ranges');
      console.log('3. Ensure vehicle types are marked as active');
      console.log('4. Example: VAN (0-50kg, 0-12CBM), LORRY (50-1000kg, 12-50CBM)');

      toast.info('Vehicle type check complete - see console for details');
    } catch (error) {
      console.error('Error checking vehicle types:', error);
    }
  };

  // Make vehicle type check available globally
  (window as any).checkVehicleTypes = checkVehicleTypes;

  // Validate manifest data
  const validateManifestData = (data: any, availableClients: Client[], availableContainers: Container[]): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Required fields validation
    if (!data.trackingNo) errors.push('Tracking number is required');
    if (!data.containerNo) errors.push('Container number is required');
    if (!data.clientUsername) errors.push('Client username is required');
    if (!data.customerName) errors.push('Customer name is required');
    if (!data.phoneNo) errors.push('Phone number is required');
    if (!data.address) errors.push('Address is required');
    if (!data.postalCode) errors.push('Postal code is required');
    if (!data.pieces || isNaN(Number(data.pieces))) errors.push('Valid pieces count is required');
    if (!data.cbm || isNaN(Number(data.cbm))) errors.push('Valid CBM is required');
    // Weight is optional - will be auto-set to 0 if empty
    if (data.weight && isNaN(Number(data.weight))) errors.push('Weight must be a valid number if provided');

    // Validate against loaded resources
    if (data.clientUsername && !availableClients.find(c => c.username === data.clientUsername)) {
      errors.push(`Client '${data.clientUsername}' not found`);
    }

    if (data.containerNo && !availableContainers.find(c => c.containerNo === data.containerNo)) {
      const availableContainerNos = availableContainers.map(c => c.containerNo).join(', ');
      errors.push(`Container '${data.containerNo}' not found. Available containers: ${availableContainerNos || 'None'}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  // Process bulk upload
  const processBulkUpload = async () => {
    if (!uploadedFile || !currentUser?.token) {
      toast.error('No file selected or user not authenticated');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setActiveStep(2);

    try {
      // Ensure resources are loaded before processing
      console.log('🔄 Ensuring resources are loaded before validation...');
      const { clients: loadedClients, containers: loadedContainers } = await loadResources();
      console.log('✅ Resources loaded:', { containers: loadedContainers.length, clients: loadedClients.length });

      // Debug: Log available containers for troubleshooting
      console.log('📦 Available containers:', loadedContainers.map(c => ({
        containerNo: c.containerNo,
        client: c.client?.username,
        status: c.status
      })));

      // Debug: Log current user info
      console.log('👤 Current user:', {
        username: currentUser?.username,
        roles: currentUser?.roles
      });

      // Parse Excel file (Excel only support)
      const rawData = await parseExcelFile(uploadedFile);

      setUploadProgress(20);
      console.log('Raw data from Excel parsing:', rawData);

      if (rawData.length === 0) {
        // Check the console for detailed debugging information
        console.error('❌ No valid data found. Check the debugging output above for details.');
        console.log('🔧 To debug your Excel file, open browser console and run:');
        console.log('   debugExcelFile(yourFile)');

        throw new Error(`❌ No valid data found in Excel file!

📋 COMMON ISSUES:
• Excel file has no data rows below headers
• First column (Tracking No) is empty
• Sample row (TRK12345) not removed from template
• Data doesn't start immediately after header row

🔍 DEBUGGING STEPS:
1. Check browser console for detailed parsing logs
2. Download a fresh template and compare structure
3. Ensure your data starts in row 2 (after headers)
4. Verify tracking numbers are in column A

🛠️ ADVANCED DEBUGGING:
Open browser console and run: debugExcelFile(yourFile)
This will show the exact structure of your Excel file.

📁 CORRECT FILE STRUCTURE:
Row 1: Tracking No* | Container No* | Client Username* | ...
Row 2: TRACK001    | CONT001       | client1          | ...
Row 3: TRACK002    | CONT002       | client2          | ...

💡 TIP: Make sure you removed the sample row (TRK12345) from the template!`);
      }

      // Validate and convert data
      const validManifests: Manifest[] = [];
      const errors: Array<{ row: number; error: string }> = [];

      for (let i = 0; i < rawData.length; i++) {
        const rowData = rawData[i];
        const validation = validateManifestData(rowData, loadedClients, loadedContainers);

        if (validation.isValid) {
          // Convert to Manifest object (simplified fields)
          const manifest: Partial<Manifest> = {
            trackingNo: rowData.trackingNo,
            client: { username: rowData.clientUsername } as Client,
            container: { containerNo: rowData.containerNo } as Container,
            customerName: rowData.customerName,
            phoneNo: rowData.phoneNo,
            address: rowData.address,
            postalCode: rowData.postalCode,
            country: 'Singapore', // Default to Singapore for all bulk uploads
            pieces: parseInt(rowData.pieces) || 0,
            cbm: parseFloat(rowData.cbm) || 0,
            weight: rowData.weight ? parseFloat(rowData.weight) || 0 : 0,
            driverRemarks: rowData.driverRemarks || '',
            remarks: rowData.remarks || '',
            status: ManifestStatus.CREATED
          };

          validManifests.push(manifest as Manifest);
        } else {
          validation.errors.forEach(error => {
            errors.push({ row: i + 2, error });
          });
        }
      }

      setUploadProgress(50);

      // Upload valid manifests in batches
      const batchSize = 10;
      let successCount = 0;

      for (let i = 0; i < validManifests.length; i += batchSize) {
        const batch = validManifests.slice(i, i + batchSize);

        try {
          console.log('🚀 Creating batch of manifests:', batch.map(m => ({
            trackingNo: m.trackingNo,
            weight: m.weight,
            cbm: m.cbm,
            pieces: m.pieces,
            deliveryVehicle: m.deliveryVehicle
          })));

          const response = await manifestService.createManifestsBulk(batch, currentUser.token);

          if (response.success) {
            successCount += batch.length;
            console.log('✅ Batch created successfully. Checking auto-assigned vehicles...');

            // Log the created manifests to see if vehicles were assigned
            if (response.data && Array.isArray(response.data)) {
              response.data.forEach((manifest: any) => {
                console.log(`📦 Manifest ${manifest.trackingNo}: deliveryVehicle = "${manifest.deliveryVehicle || 'EMPTY'}", weight = ${manifest.weight}, cbm = ${manifest.cbm}, pieces = ${manifest.pieces}`);
              });

              // Emit event for real-time manifest list updates
              const manifestCreatedEvent = new CustomEvent('manifests-bulk-created', {
                detail: {
                  type: 'bulk_created',
                  manifests: response.data,
                  count: response.data.length,
                  timestamp: new Date().toISOString()
                }
              });
              window.dispatchEvent(manifestCreatedEvent);
              console.log(`📡 Emitted bulk manifest creation event: ${response.data.length} manifests created`);
            }
          } else {
            // Add batch errors
            batch.forEach((_, index) => {
              errors.push({
                row: i + index + 2,
                error: response.message || 'Failed to create manifest'
              });
            });
          }
        } catch (batchError: any) {
          // Add batch errors
          batch.forEach((_, index) => {
            errors.push({
              row: i + index + 2,
              error: batchError.message || 'Failed to create manifest'
            });
          });
        }

        // Update progress
        const progress = 50 + ((i + batchSize) / validManifests.length) * 50;
        setUploadProgress(Math.min(progress, 100));
      }

      // Set results
      setBulkUploadResults({
        total: rawData.length,
        successful: successCount,
        failed: rawData.length - successCount,
        errors
      });

      // Move to results step
      setActiveStep(2);

      if (successCount > 0) {
        toast.success(`Successfully created ${successCount} manifest(s)!`);
        if (errors.length === 0) {
          toast.info('✅ All manifests created successfully and added to the list automatically. No page refresh needed!');
          setTimeout(() => {
            toast.info('🚛 Vehicles should be auto-assigned based on weight/CBM. Check browser console for assignment details.');
          }, 2000);
        } else {
          toast.warning(`${successCount} succeeded, ${errors.length} failed. Successful manifests have been added to the list automatically.`);
        }
        console.log('🔍 To check vehicle assignments, look for the manifest creation logs above.');
        console.log('💡 If vehicles show as "Auto-assign", check if vehicle types are configured in the system.');
        console.log('📋 New manifests have been automatically added to the manifest list without refresh.');
      } else if (errors.length > 0) {
        toast.error(`Upload failed: ${errors.length} error(s) found. Check details below.`);
      }

      if (errors.length > 0) {
        toast.warning(`${errors.length} errors occurred during upload`);
      }

    } catch (error: any) {
      console.error('Bulk upload error:', error);
      toast.error(error.message || 'Failed to process bulk upload');

      setBulkUploadResults({
        total: 0,
        successful: 0,
        failed: 1,
        errors: [{ row: 0, error: error.message || 'Upload failed' }]
      });

      setActiveStep(3);
    } finally {
      setIsUploading(false);
      setUploadProgress(100);
    }
  };

  // Excel template download (matching Container Details page style exactly)
  const downloadTemplate = async () => {
    try {
      // Create new workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Manifest Template');

      // Define template columns (headers match actual ManifestList column headers exactly)
      const columns = [
        { header: 'Tracking', key: 'trackingNo', width: 20 },
        { header: 'Cust', key: 'customerName', width: 25 },
        { header: 'Address', key: 'address', width: 35 },
        { header: 'Postal Code', key: 'postalCode', width: 15 },
        { header: 'Phone', key: 'phoneNo', width: 18 },
        { header: 'Pcs', key: 'pieces', width: 12 },
        { header: 'CBM', key: 'cbm', width: 12 },
        { header: 'For Driver Only', key: 'driverRemarks', width: 30 },
        { header: 'For CS Only', key: 'remarks', width: 30 },
        { header: 'Weight (kg)', key: 'weight', width: 15 },
        { header: 'Container', key: 'containerNo', width: 18 },
        { header: 'Client', key: 'clientUsername', width: 18 },
      ];

      // Add columns to worksheet
      worksheet.columns = columns;

      // Style the header row (exact same style as Container Details)
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4F81BD' } // Blue background (same as Container Details)
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' } // White text
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      // Add one sample row to demonstrate data entry (reordered to match ManifestList)
      const sampleRow = {
        trackingNo: 'TRK12345',
        customerName: 'John Doe',
        address: '123 Orchard Road, #12-34, Singapore',
        postalCode: '238874',
        phoneNo: '+65 9123 4567',
        pieces: 5,
        cbm: 2.5,
        driverRemarks: 'Fragile items',
        remarks: 'Handle with care',
        weight: 100,
        containerNo: 'CONT001',
        clientUsername: 'client1'
      };

      // Apply the sample data to the first data row (row 2) - reordered to match sequence
      const firstDataRow = worksheet.getRow(2);
      firstDataRow.getCell(1).value = sampleRow.trackingNo;
      firstDataRow.getCell(2).value = sampleRow.customerName;
      firstDataRow.getCell(3).value = sampleRow.address;
      firstDataRow.getCell(4).value = sampleRow.postalCode;
      firstDataRow.getCell(5).value = sampleRow.phoneNo;
      firstDataRow.getCell(6).value = sampleRow.pieces;
      firstDataRow.getCell(7).value = sampleRow.cbm;
      firstDataRow.getCell(8).value = sampleRow.driverRemarks;
      firstDataRow.getCell(9).value = sampleRow.remarks;
      firstDataRow.getCell(10).value = sampleRow.weight;
      firstDataRow.getCell(11).value = sampleRow.containerNo;
      firstDataRow.getCell(12).value = sampleRow.clientUsername;

      // Style the sample data cells (simplified to 12 columns)
      for (let col = 1; col <= 12; col++) {
        const cell = firstDataRow.getCell(col);
        cell.font = {
          italic: true,
          color: { argb: '808080' } // Gray text for example data
        };
      }

      // Add instructions (updated for reordered columns)
      worksheet.addRow([]);
      worksheet.addRow(['Instructions:']);
      worksheet.addRow(['1. Fields marked with * are required']);
      worksheet.addRow(['2. Weight is optional - will default to 0 if empty']);
      worksheet.addRow(['3. Country will automatically be set to Singapore for all manifests']);
      worksheet.addRow(['4. Container numbers and client usernames must exist in the system']);
      worksheet.addRow(['5. Column order matches the ManifestList display sequence']);
      worksheet.addRow(['6. Delete the sample row before uploading your data']);
      worksheet.addRow(['7. All validation errors must be fixed before any manifests are created']);

      // Generate timestamp for filename (matching Container Details format)
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const filename = `manifest_bulk_upload_template_${timestamp}.xlsx`;

      // Generate buffer and save (exact same approach as Container Details)
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Template downloaded successfully');
    } catch (error) {
      console.error('Error generating template:', error);
      toast.error('Failed to generate template');
    }
  };

  // Export errors to Excel for easier fixing
  const exportErrorsToExcel = async () => {
    if (!bulkUploadResults || bulkUploadResults.errors.length === 0) return;

    try {
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Upload Errors');

      // Add headers
      worksheet.columns = [
        { header: 'Row Number', key: 'row', width: 15 },
        { header: 'Error Description', key: 'error', width: 50 },
        { header: 'How to Fix', key: 'solution', width: 60 }
      ];

      // Style header
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D32F2F' } // Red background for errors
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFF' }
        };
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      // Add error data with solutions
      bulkUploadResults.errors.forEach((error) => {
        let solution = 'Please check the data and fix the issue';

        // Provide specific solutions based on error type
        if (error.error.includes('required')) {
          solution = 'Fill in the missing required field marked with * in the template';
        } else if (error.error.includes('not found')) {
          solution = 'Ensure the referenced item exists in the system or use a valid value';
        } else if (error.error.includes('tracking')) {
          solution = 'Use a unique tracking number that does not already exist';
        } else if (error.error.includes('format') || error.error.includes('invalid')) {
          solution = 'Check the data format - numbers should be numeric, dates should be YYYY-MM-DD, weight is optional';
        }

        worksheet.addRow({
          row: error.row,
          error: error.error,
          solution: solution
        });
      });

      // Add instructions
      worksheet.addRow([]);
      worksheet.addRow(['Instructions:']);
      worksheet.addRow(['1. Review each error in your original Excel file']);
      worksheet.addRow(['2. Fix the issues according to the "How to Fix" column']);
      worksheet.addRow(['3. Save your corrected Excel file']);
      worksheet.addRow(['4. Use the "Reupload Corrected File" button to try again']);

      // Generate and download
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const filename = `upload_errors_${timestamp}.xlsx`;

      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Error report exported successfully');
    } catch (error) {
      console.error('Error exporting errors:', error);
      toast.error('Failed to export error report');
    }
  };

  // ===== CREATE MANIFEST FUNCTIONS =====

  // Load drivers for the form
  const loadDrivers = async () => {
    if (!currentUser?.token) return;

    try {
      const response = await userService.getDrivers(currentUser.token);
      if (response.success) {
        setDrivers(response.data);
      }
    } catch (error) {
      console.error('Error loading drivers:', error);
    }
  };

  // Load location zones (matching ManifestManagement)
  const loadLocationZones = async () => {
    console.log('🏢 Starting to load location zones...');
    try {
      if (!currentUser?.token) {
        console.error('❌ Authentication token is missing for location zones');
        return;
      }

      console.log('🏢 Token available, calling API...');
      setLocationsLoading(true);
      const response = await locationService.getActiveLocationZones(currentUser.token);

      console.log('🏢 Location zones API response:', response);

      if (response.success && response.data) {
        console.log(`✅ Loaded ${response.data.length} location zones:`, response.data);
        setLocationZones(response.data);
      } else {
        console.error('❌ Failed to fetch location zones:', response.message);
        toast.error('Failed to load location zones. Location selection may be unavailable.');
      }
    } catch (err: any) {
      console.error('❌ Error fetching location zones:', err);
      toast.error('Error loading location zones. Location selection may be unavailable.');
    } finally {
      setLocationsLoading(false);
    }
  };

  // Load vehicle types (matching ManifestManagement)
  const loadVehicleTypes = async () => {
    console.log('🚛 Starting to load vehicle types...');
    try {
      setVehicleTypesLoading(true);
      const data = await vehicleService.getVehicleTypes();

      console.log('🚛 Vehicle types API response:', data);

      if (data && Array.isArray(data)) {
        console.log(`✅ Loaded ${data.length} vehicle types:`, data);
        setVehicleTypes(data);
      } else {
        console.error('❌ Invalid vehicle types data:', data);
        toast.error('Failed to load vehicle types. Vehicle type selection may be unavailable.');
      }
    } catch (err: any) {
      console.error('❌ Error fetching vehicle types:', err);
      toast.error('Error loading vehicle types. Vehicle type selection may be unavailable.');
    } finally {
      setVehicleTypesLoading(false);
    }
  };

  // Open create manifest dialog
  const handleOpenCreateManifest = () => {
    console.log('Opening create manifest dialog');
    console.log('Location zones available:', locationZones.length);
    console.log('Vehicle types available:', vehicleTypes.length);
    console.log('Drivers available:', drivers.length);

    setCreateManifestDialogOpen(true);
    setFormError('');
    // Reset form data
    setFormData({
      trackingNo: '',
      client: { username: '' },
      container: { containerNo: '' },
      driver: { username: '' },
      sequenceNo: 0,
      status: ManifestStatus.CREATED,
      customerName: '',
      phoneNo: '',
      address: '',
      postalCode: '',
      country: '',
      pieces: 0,
      cbm: 0,
      actualPalletsCount: 0,
      weight: 0,
      location: '',
      deliveryDate: null,
      timeSlot: '',
      deliveredDate: null,
      deliveryVehicle: '',
      driverRemarks: '',
      remarks: '',
      internalId: '',
      createdDate: null,
    });
  };

  // Close create manifest dialog
  const handleCloseCreateManifest = () => {
    setCreateManifestDialogOpen(false);
    setFormError('');

    // Clear any focus issues by ensuring proper cleanup
    // Remove any aria-hidden attributes that might be causing issues
    setTimeout(() => {
      // Remove aria-hidden from root if it exists
      const rootElement = document.getElementById('root');
      if (rootElement && rootElement.getAttribute('aria-hidden') === 'true') {
        rootElement.removeAttribute('aria-hidden');
      }

      // Allow focus to return to the trigger button
      const createButton = document.querySelector('[aria-label="Create Manifest"]') as HTMLElement;
      if (createButton) {
        createButton.focus();
      }
    }, 150);
  };

  // Handle form field changes
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle input change (matching ManifestManagement)
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle location change (matching ManifestManagement)
  const handleLocationChange = (e: SelectChangeEvent) => {
    setFormData(prev => ({ ...prev, location: e.target.value }));
    console.log(`Location zone selected: ${e.target.value}`);
  };

  // Handle date change (matching ManifestManagement)
  const handleDateChange = (name: string, value: Date | null) => {
    // Update the form data with the new date value
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle time slot change (matching ManifestManagement)
  const handleTimeSlotChange = (timeSlotId: string | null) => {
    setFormData(prev => ({
      ...prev,
      timeSlot: timeSlotId || ''
    }));
  };



  // Handle client selection and reset dependent fields
  const handleClientChange = (clientUsername: string) => {
    const selectedClient = clients.find(client => client.username === clientUsername);

    // Reset all dependent fields when client changes (matching ManifestManagement behavior)
    setFormData(prev => ({
      ...prev,
      client: selectedClient || { username: clientUsername },
      container: { containerNo: '' }, // Reset container selection when client changes
      sequenceNo: 0,
      internalId: '',
      // Reset other fields that might be dependent on client context
      location: '',
      deliveryDate: null,
      timeSlot: '',
      driver: { username: '' },
      deliveryVehicle: ''
    }));
  };

  // Get filtered containers based on selected client
  const getFilteredContainers = () => {
    if (!formData.client?.username) {
      console.log('No client selected for container filtering');
      return []; // No client selected, show no containers
    }

    // Debug log for container filtering
    console.log('Filtering containers for client:', formData.client.username);
    console.log('Currently selected container:', formData.container.containerNo);

    // Filter containers to only show those belonging to the selected client
    const filteredContainers = containers.filter(container =>
      container.client?.username === formData.client.username
    );

    console.log('Filtered containers:', filteredContainers);
    return filteredContainers;
  };

  // Handle container selection and auto-assign sequence number
  const handleContainerChange = async (containerNo: string) => {
    setFormData(prev => ({
      ...prev,
      container: { containerNo }
    }));

    // Auto-assign sequence number if creating new manifest
    if (containerNo && currentUser?.token) {
      try {
        const response = await manifestService.getNextSequenceNumber(containerNo, currentUser.token);
        if (response.success) {
          const nextSequence = response.data;
          const internalId = `${containerNo}-${nextSequence}`;

          setFormData(prev => ({
            ...prev,
            sequenceNo: nextSequence,
            internalId: internalId
          }));
        }
      } catch (error) {
        console.error('Error getting next sequence number:', error);
      }
    }
  };

  // Save manifest
  const handleSaveManifest = async () => {
    if (!currentUser?.token) {
      setFormError('Authentication token not found');
      return;
    }

    setFormLoading(true);
    setFormError('');

    try {
      // Validate required fields
      if (!formData.trackingNo.trim()) {
        setFormError('Tracking number is required');
        setFormLoading(false);
        return;
      }
      if (!formData.client.username) {
        setFormError('Client is required');
        setFormLoading(false);
        return;
      }
      if (!formData.container.containerNo) {
        setFormError('Container is required');
        setFormLoading(false);
        return;
      }
      if (!formData.customerName.trim()) {
        setFormError('Customer name is required');
        setFormLoading(false);
        return;
      }

      // Find full client and container objects
      const fullClient = clients.find(c => c.username === formData.client.username);
      const fullContainer = containers.find(c => c.containerNo === formData.container.containerNo);
      const fullDriver = drivers.find(d => d.username === formData.driver.username);

      if (!fullClient) {
        setFormError('Selected client not found');
        setFormLoading(false);
        return;
      }
      if (!fullContainer) {
        setFormError('Selected container not found');
        setFormLoading(false);
        return;
      }

      // Create manifest object
      const manifestToSave: Manifest = {
        trackingNo: formData.trackingNo.trim(),
        client: fullClient,
        container: fullContainer,
        driver: fullDriver as any,
        sequenceNo: formData.sequenceNo,
        status: formData.status,
        customerName: formData.customerName.trim(),
        phoneNo: formData.phoneNo.trim(),
        address: formData.address.trim(),
        postalCode: formData.postalCode.trim(),
        country: formData.country.trim(),
        pieces: formData.pieces,
        cbm: formData.cbm,
        actualPalletsCount: formData.actualPalletsCount,
        weight: formData.weight || 0, // Default to 0 if empty
        location: formData.location.trim(),
        deliveryDate: formData.deliveryDate ? formData.deliveryDate.toISOString().split('T')[0] : null,
        timeSlot: formData.timeSlot || null,
        deliveredDate: formData.deliveredDate ? formData.deliveredDate.toISOString() : null,
        deliveryVehicle: formData.deliveryVehicle.trim(),
        driverRemarks: formData.driverRemarks.trim(),
        remarks: formData.remarks.trim(),
        internalId: formData.internalId
      };

      const response = await manifestService.createManifest(manifestToSave, currentUser.token);

      if (response.success) {
        toast.success('Manifest created successfully');
        handleCloseCreateManifest();

        // Emit event for real-time manifest list updates
        const manifestCreatedEvent = new CustomEvent('manifest-created', {
          detail: {
            type: 'created',
            manifest: response.data,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(manifestCreatedEvent);
      } else {
        setFormError(response.message || 'Failed to create manifest');
      }
    } catch (error: any) {
      console.error('Error creating manifest:', error);
      setFormError(error.response?.data?.message || 'Failed to create manifest');
    } finally {
      setFormLoading(false);
    }
  };

  // Load resources on component mount
  useEffect(() => {
    console.log('Loading resources on component mount, currentUser:', currentUser?.username);
    loadResources();
    loadDrivers();
    loadLocationZones();
    loadVehicleTypes();
  }, [currentUser]);

  // Focus management for dialog
  useEffect(() => {
    if (createManifestDialogOpen && firstInputRef.current) {
      // Ensure proper focus management when dialog opens
      setTimeout(() => {
        // Remove any conflicting aria-hidden attributes
        const rootElement = document.getElementById('root');
        if (rootElement && rootElement.getAttribute('aria-hidden') === 'true') {
          rootElement.removeAttribute('aria-hidden');
        }

        // Focus the first input
        firstInputRef.current?.focus();
      }, 150);
    }
  }, [createManifestDialogOpen]);

  // Update width on resize (for display only)
  useEffect(() => {
    // Initial measurement after a short delay to ensure layout is complete
    const initialTimer = setTimeout(() => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    }, 500);
    
    // Update on resize with debounce
    window.addEventListener('resize', debouncedUpdateWidth);
    
    // Use MutationObserver only for significant layout changes
    const observer = new MutationObserver(debouncedUpdateWidth);
    if (containerRef.current) {
      observer.observe(document.body, { 
        childList: false,
        subtree: false,
        attributes: true,
        attributeFilter: ['class', 'style'] // Only observe class and style changes
      });
    }
    
    return () => {
      window.removeEventListener('resize', debouncedUpdateWidth);
      observer.disconnect();
      clearTimeout(initialTimer);
    };
  }, [debouncedUpdateWidth]);
  
  return (
    <Box 
      ref={containerRef}
      sx={{ 
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 0 // Remove padding
      }}
    >
      {/* The ManifestList component will fetch data from the API */}
      <Box sx={{ 
        flexGrow: 1, 
        width: '100%',
        px: 0 // Remove padding
      }}>
        <Suspense fallback={<LoadingFallback />}>
          <ManifestList
            onManifestDelete={handleManifestDelete}
            onBulkDelete={handleBulkManifestDelete}
            onCreateManifest={handleOpenCreateManifest}
            onUploadManifest={handleOpenBulkUpload}
          />
        </Suspense>

        {/* ===== ENHANCED BULK UPLOAD DIALOG ===== */}
        <Dialog
          open={bulkUploadDialogOpen}
          onClose={() => setBulkUploadDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle sx={{
            bgcolor: 'primary.main',
            color: 'white',
            px: 3,
            py: 2
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Box display="flex" alignItems="center">
                <CloudUploadIcon sx={{ mr: 2 }} />
                Professional Excel Bulk Upload
              </Box>
              <IconButton
                onClick={() => setBulkUploadDialogOpen(false)}
                size="small"
                sx={{ color: 'white' }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent dividers>
            {resourcesLoading && <LinearProgress sx={{ mb: 2 }} />}

            <Stepper activeStep={activeStep} orientation="vertical">
              {/* Step 1: File Selection */}
              <Step>
                <StepLabel>Select Excel File</StepLabel>
                <StepContent>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Excel Template Available
                    </Typography>
                    <Typography variant="body2">
                      Download the formatted Excel template with sample data,
                      validation rules, and detailed instructions.
                    </Typography>
                  </Alert>

                  {/* Show reupload guidance if coming back from errors */}
                  {activeStep === 0 && bulkUploadResults && bulkUploadResults.failed > 0 && (
                    <Alert severity="warning" sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        🔄 Reupload Mode
                      </Typography>
                      <Typography variant="body2">
                        Fix the errors in your Excel file and upload the corrected version.
                        You can review the error details in the results section below.
                      </Typography>
                    </Alert>
                  )}

                  {!uploadedFile ? (
                    <Paper
                      sx={{
                        p: 4,
                        border: '2px dashed #ccc',
                        mb: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        bgcolor: 'rgba(46, 125, 50, 0.04)',
                        '&:hover': {
                          bgcolor: 'rgba(46, 125, 50, 0.08)',
                          borderColor: 'primary.main'
                        }
                      }}
                      onClick={() => fileInputRef.current?.click()}
                      onDrop={handleFileDrop}
                      onDragOver={handleDragOver}
                    >
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept=".xlsx,.xls"
                        onChange={handleFileChange}
                        style={{ display: 'none' }}
                      />

                      <CloudUploadIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        Drop Excel File Here or Click to Browse
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Supports .xlsx and .xls files up to 10MB
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<UploadFileIcon />}
                        sx={{ mt: 1 }}
                      >
                        Choose Excel File
                      </Button>
                    </Paper>
                  ) : (
                    <Paper sx={{
                      p: 3,
                      mb: 2,
                      bgcolor: 'success.light',
                      color: 'success.contrastText',
                      border: '2px solid',
                      borderColor: 'success.main'
                    }}>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <CheckCircleIcon sx={{ fontSize: 32 }} />
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="subtitle1" fontWeight="bold">
                            ✅ File Selected: {uploadedFile.name}
                          </Typography>
                          <Typography variant="body2">
                            Size: {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB | Ready for upload
                          </Typography>
                        </Box>
                        <Stack direction="row" spacing={1}>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={async () => {
                              console.log('🔍 Manual debug analysis...');
                              await debugExcelFile(uploadedFile);
                              toast.info('Debug analysis complete - check browser console');
                            }}
                            sx={{ color: 'inherit', borderColor: 'currentColor' }}
                          >
                            Debug File
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={handleResetFileUpload}
                            sx={{ color: 'inherit', borderColor: 'currentColor' }}
                          >
                            Change File
                          </Button>
                        </Stack>
                      </Stack>
                    </Paper>
                  )}

                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Important Validation Rules:
                    </Typography>
                    <Typography variant="body2">
                      • All required fields must be filled (marked with * in template)<br/>
                      • If ANY row has validation errors, NO manifests will be created<br/>
                      • Delete sample rows before uploading your data<br/>
                      • Tracking numbers must be unique and not exist in system
                    </Typography>
                  </Alert>

                  {/* Navigation buttons for step 1 */}
                  {uploadedFile && (
                    <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                      <Button
                        variant="contained"
                        onClick={() => setActiveStep(1)}
                        startIcon={<ArrowForwardIcon />}
                      >
                        Next: Validate & Upload
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={downloadTemplate}
                        startIcon={<GetAppIcon />}
                      >
                        Download Template
                      </Button>
                    </Stack>
                  )}
                </StepContent>
              </Step>

              {/* Step 2: Validation */}
              <Step>
                <StepLabel>Validate & Upload</StepLabel>
                <StepContent>
                  <Typography variant="body1" gutterBottom>
                    File ready for processing. Click "Start Upload" to begin validation and upload.
                  </Typography>

                  <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      onClick={processBulkUpload}
                      disabled={isUploading}
                      startIcon={isUploading ? <CircularProgress size={20} /> : <CloudUploadIcon />}
                    >
                      {isUploading ? 'Processing...' : 'Start Upload'}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => setActiveStep(0)}
                      disabled={isUploading}
                    >
                      Back
                    </Button>
                  </Stack>
                </StepContent>
              </Step>

              {/* Step 3: Results */}
              <Step>
                <StepLabel>Upload Results</StepLabel>
                <StepContent>
                  {isUploading && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Processing... {uploadProgress}%
                      </Typography>
                      <LinearProgress variant="determinate" value={uploadProgress} />
                    </Box>
                  )}

                  {!isUploading && bulkUploadResults && (
                    <Box sx={{ textAlign: 'center', py: 2 }}>
                      <CheckCircleIcon sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
                      <Typography variant="h6" gutterBottom>
                        Upload Complete!
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        See detailed results below the stepper.
                      </Typography>
                    </Box>
                  )}

                  {!isUploading && !bulkUploadResults && (
                    <Typography variant="body2" color="text.secondary">
                      Upload will begin once you click "Start Upload" in the previous step.
                    </Typography>
                  )}
                </StepContent>
              </Step>
            </Stepper>

            {/* Always visible results section when upload is complete */}
            {bulkUploadResults && (
              <Box sx={{ mt: 3, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                  📊 Upload Results Summary
                </Typography>

                <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 2,
                      flex: 1,
                      textAlign: 'center',
                      bgcolor: 'rgba(25, 118, 210, 0.1)'
                    }}
                  >
                    <Typography variant="h4" color="primary.main" fontWeight="bold">
                      {bulkUploadResults.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">Total Processed</Typography>
                  </Paper>
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      border: '1px solid',
                      borderColor: 'success.main',
                      borderRadius: 2,
                      flex: 1,
                      textAlign: 'center',
                      bgcolor: 'rgba(46, 125, 50, 0.1)'
                    }}
                  >
                    <Typography variant="h4" color="success.main" fontWeight="bold">
                      {bulkUploadResults.successful}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">Successfully Created</Typography>
                  </Paper>
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      border: '1px solid',
                      borderColor: 'error.main',
                      borderRadius: 2,
                      flex: 1,
                      textAlign: 'center',
                      bgcolor: 'rgba(211, 47, 47, 0.1)'
                    }}
                  >
                    <Typography variant="h4" color="error.main" fontWeight="bold">
                      {bulkUploadResults.failed}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">Failed/Errors</Typography>
                  </Paper>
                </Stack>

                {bulkUploadResults.errors.length > 0 && (
                  <Box>
                    <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => setShowErrorDetails(!showErrorDetails)}
                        startIcon={showErrorDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      >
                        {showErrorDetails ? 'Hide' : 'Show'} Error Details ({bulkUploadResults.errors.length})
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        color="error"
                        startIcon={<GetAppIcon />}
                        onClick={exportErrorsToExcel}
                      >
                        Export Error Report
                      </Button>
                    </Stack>

                    <Collapse in={showErrorDetails}>
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          💡 How to Fix Common Errors:
                        </Typography>
                        <Typography variant="body2" component="div">
                          • <strong>Missing required field:</strong> Fill in all fields marked with * in the template<br/>
                          • <strong>Container not found:</strong> You can only use containers assigned to your account. Check available containers in the dropdown when creating individual manifests<br/>
                          • <strong>Client not found:</strong> Ensure client usernames exist in the system and you have access to them<br/>
                          • <strong>Invalid tracking number:</strong> Use unique tracking numbers that don't already exist<br/>
                          • <strong>Invalid data format:</strong> Check that numbers are valid and dates are in YYYY-MM-DD format<br/>
                          • <strong>Weight field:</strong> Weight is optional and will default to 0 if left empty
                        </Typography>
                      </Alert>

                      {/* Show available containers for debugging */}
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          📦 Your Available Containers:
                        </Typography>
                        <Typography variant="body2" component="div">
                          {containers.length > 0 ? (
                            containers.map(container => (
                              <span key={container.containerNo}>
                                <strong>{container.containerNo}</strong> (Client: {container.client?.username || 'Unknown'})
                                <br />
                              </span>
                            ))
                          ) : (
                            <em>No containers available. Please contact your administrator to assign containers to your account.</em>
                          )}
                        </Typography>
                      </Alert>

                      <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 300 }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell sx={{ fontWeight: 'bold' }}>Row</TableCell>
                              <TableCell sx={{ fontWeight: 'bold' }}>Error</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {bulkUploadResults.errors.map((error, index) => (
                              <TableRow key={index}>
                                <TableCell>{error.row}</TableCell>
                                <TableCell>{error.error}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Collapse>
                  </Box>
                )}

                {bulkUploadResults.successful > 0 && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      ✅ Successfully created {bulkUploadResults.successful} manifest(s).
                      <strong>New manifests have been automatically added to the list below - no refresh needed!</strong>
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                      🚛 <strong>Vehicle Assignment:</strong> Vehicles are auto-assigned based on weight, CBM, and pieces.
                      If manifests show "Auto-assign", check that vehicle types are configured in the system
                      and match your manifest specifications.
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic', color: 'success.dark' }}>
                      📋 <strong>Real-time Updates:</strong> The manifest list is automatically updated with new entries.
                      You can continue working without any page refreshes.
                    </Typography>
                  </Alert>
                )}

                {bulkUploadResults.failed > 0 && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      ❌ {bulkUploadResults.failed} manifest(s) failed to create.
                      Please review the error details above and fix the issues in your Excel file.
                    </Typography>
                    <Box sx={{ mt: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
                      <Button
                        variant="contained"
                        color="error"
                        size="small"
                        startIcon={<UploadFileIcon />}
                        onClick={() => {
                          // Reset to step 1 for reupload
                          setActiveStep(0);
                          setUploadedFile(null);
                          setBulkUploadResults(null);
                          setUploadProgress(0);
                          setIsUploading(false);
                          if (fileInputRef.current) {
                            fileInputRef.current.value = '';
                          }
                        }}
                        sx={{ borderRadius: 2 }}
                      >
                        Upload Corrected File
                      </Button>
                      <Typography variant="caption" color="text.secondary">
                        Fix the errors in your Excel file and upload again
                      </Typography>
                    </Box>
                  </Alert>
                )}
              </Box>
            )}
          </DialogContent>

          <DialogActions sx={{ p: 3, bgcolor: 'grey.50' }}>
            <Button
              onClick={downloadTemplate}
              startIcon={<GetAppIcon />}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              Download Excel Template
            </Button>

            {/* Show reupload button when there are errors */}
            {bulkUploadResults && bulkUploadResults.failed > 0 && (
              <Button
                variant="contained"
                color="warning"
                startIcon={<UploadFileIcon />}
                onClick={() => {
                  // Reset to step 1 for reupload
                  setActiveStep(0);
                  setUploadedFile(null);
                  setBulkUploadResults(null);
                  setUploadProgress(0);
                  setIsUploading(false);
                  if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                  }
                  toast.info('Ready for reupload. Please select your corrected Excel file.');
                }}
                sx={{ borderRadius: 2 }}
              >
                Reupload Corrected File
              </Button>
            )}

            {/* Show vehicle type check button when upload is complete */}
            {bulkUploadResults && (
              <Button
                variant="outlined"
                color="info"
                onClick={checkVehicleTypes}
                sx={{ borderRadius: 2 }}
              >
                Check Vehicle Types
              </Button>
            )}

            {/* Show upload another file button when successful */}
            {bulkUploadResults && bulkUploadResults.successful > 0 && bulkUploadResults.failed === 0 && (
              <Button
                variant="outlined"
                onClick={handleResetFileUpload}
                sx={{ borderRadius: 2 }}
              >
                Upload Another File
              </Button>
            )}

            <Button
              onClick={() => setBulkUploadDialogOpen(false)}
              variant="contained"
              sx={{ borderRadius: 2 }}
            >
              {bulkUploadResults ? 'Done' : 'Cancel'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Create Manifest Dialog - Matching ManifestManagement Style */}
        <Dialog
          open={createManifestDialogOpen}
          onClose={handleCloseCreateManifest}
          maxWidth="lg"
          fullWidth
          disableEscapeKeyDown={false}
          disableRestoreFocus={false}
          keepMounted={false}
          aria-labelledby="create-manifest-dialog-title"
          aria-describedby="create-manifest-dialog-description"
          sx={{
            '& .MuiDialog-paper': {
              borderRadius: 3,
              boxShadow: 24,
              position: 'relative',
              zIndex: 1300
            },
            '& .MuiBackdrop-root': {
              zIndex: 1299
            }
          }}
          slotProps={{
            backdrop: {
              sx: {
                zIndex: 1299
              }
            }
          }}
        >
          <DialogTitle sx={{
            bgcolor: 'primary.main',
            color: 'white',
            px: 3,
            py: 2
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6" id="create-manifest-dialog-title">
                Create Manifest
              </Typography>
              <IconButton
                aria-label="close"
                onClick={handleCloseCreateManifest}
                sx={{
                  color: 'white',
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent dividers sx={{ px: 3, py: 3, '&::-webkit-scrollbar': { width: 8 }, '&::-webkit-scrollbar-thumb': { backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: 4 } }}>
            {/* Hidden description for screen readers */}
            <Typography id="create-manifest-dialog-description" sx={{ display: 'none' }}>
              Create a new manifest by filling out the required information including tracking number, client, container, and customer details.
            </Typography>

            {formError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {formError}
              </Alert>
            )}

            <form onSubmit={(e) => { e.preventDefault(); handleSaveManifest(); }}>
              {/* Identification Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Identification
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  required
                  name="trackingNo"
                  label="Tracking No"
                  value={formData.trackingNo}
                  onChange={(e) => handleFormChange('trackingNo', e.target.value)}
                  fullWidth
                  margin="normal"
                  inputRef={firstInputRef}
                  autoFocus
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Internal ID
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{
                      color: 'primary.main',
                      display: 'inline-flex',
                      alignItems: 'center',
                      '& svg': { mr: 1 }
                    }}>
                      <AddIcon fontSize="small" />
                    </Box>
                    {formData.container?.containerNo && formData.sequenceNo
                      ? `${formData.container.containerNo}-${formData.sequenceNo}`
                      : (formData.internalId || 'Auto-generated from container number and sequence')}
                  </Typography>
                </Box>
              </Box>

              {/* Client Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Client Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="client-label">Client</InputLabel>
                  <Select
                    labelId="client-label"
                    name="client"
                    value={formData.client.username}
                    onChange={(e) => handleClientChange(e.target.value)}
                    label="Client"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    required
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }}
                  >
                    {clients && clients.length > 0 ? (
                      clients.map((client) => (
                        <MenuItem key={client.username} value={client.username}>
                          {`${client.username} - ${client.companyName || 'No Company'}`}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled>No clients available</MenuItem>
                    )}
                  </Select>
                </FormControl>

                <FormControl fullWidth margin="normal">
                  <InputLabel id="container-label">Container</InputLabel>
                  <Select
                    labelId="container-label"
                    name="container"
                    value={formData.container.containerNo}
                    onChange={(e) => handleContainerChange(e.target.value)}
                    label="Container"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    required
                    disabled={!formData.client.username} // Disable if no client selected
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }}
                  >
                    {!formData.client.username ? (
                      <MenuItem disabled>Please select a client first</MenuItem>
                    ) : getFilteredContainers().length > 0 ? (
                      getFilteredContainers().map((container) => (
                        <MenuItem key={container.containerNo} value={container.containerNo}>
                          {container.containerNo}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled>No containers available for this client</MenuItem>
                    )}
                  </Select>
                </FormControl>
              </Box>

              {/* Shipping Information Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Shipping Information
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  required
                  name="customerName"
                  label="Customer Name"
                  value={formData.customerName}
                  onChange={(e) => handleFormChange('customerName', e.target.value)}
                  fullWidth
                  margin="normal"
                  slotProps={{
                    input: {
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="action" />
                        </InputAdornment>
                      ),
                    },
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />

                <TextField
                  required
                  name="phoneNo"
                  label="Phone Number"
                  value={formData.phoneNo}
                  onChange={(e) => handleFormChange('phoneNo', e.target.value)}
                  fullWidth
                  margin="normal"
                  slotProps={{
                    input: {
                      startAdornment: (
                        <InputAdornment position="start">
                          <PhoneIcon color="action" />
                        </InputAdornment>
                      ),
                    },
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />

                <TextField
                  required
                  name="address"
                  label="Address"
                  value={formData.address}
                  onChange={(e) => handleFormChange('address', e.target.value)}
                  fullWidth
                  margin="normal"
                  sx={{
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                  slotProps={{
                    input: {
                      startAdornment: (
                        <InputAdornment position="start">
                          <HomeIcon color="action" />
                        </InputAdornment>
                      ),
                    },
                  }}
                />

                <TextField
                  required
                  name="postalCode"
                  label="Postal Code"
                  value={formData.postalCode}
                  onChange={(e) => handleFormChange('postalCode', e.target.value)}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />

                <TextField
                  required
                  name="country"
                  label="Country"
                  value={formData.country}
                  onChange={(e) => handleFormChange('country', e.target.value)}
                  fullWidth
                  margin="normal"
                  slotProps={{
                    input: {
                      startAdornment: (
                        <InputAdornment position="start">
                          <LocationOnIcon color="action" />
                        </InputAdornment>
                      ),
                    },
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />
              </Box>

              {/* Package Details Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Package Details
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  required
                  name="pieces"
                  label="Pieces"
                  type="number"
                  value={formData.pieces || ''}
                  onChange={(e) => handleFormChange('pieces', parseInt(e.target.value) || 0)}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />

                <TextField
                  required
                  name="cbm"
                  label="CBM"
                  type="number"
                  value={formData.cbm || ''}
                  onChange={(e) => handleFormChange('cbm', parseFloat(e.target.value) || 0)}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />

                <Box sx={{ mt: 2, mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Number of Pallets</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, mt: 1 }}>
                    {formData.actualPalletsCount ?? 0}
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                      The number of pallets is automatically calculated based on created pallets
                    </Typography>
                  </Typography>
                </Box>

                <FormControl
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                >
                  <InputLabel id="location-label">Location</InputLabel>
                  <Select
                    labelId="location-label"
                    id="location"
                    name="location"
                    value={formData.location || ''}
                    onChange={handleLocationChange}
                    label="Location"
                    startAdornment={
                      <InputAdornment position="start">
                        <LocationOnIcon color="action" />
                      </InputAdornment>
                    }
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {locationZones && locationZones.length > 0 ? (
                      locationZones.map((zone) => (
                        <MenuItem key={zone.id} value={zone.name}>
                          {zone.name}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled>
                        {locationsLoading ? 'Loading...' : 'No location zones available'}
                      </MenuItem>
                    )}
                  </Select>
                  <FormHelperText>
                    {locationsLoading ? 'Loading location zones...' : 'Select a location zone'}
                  </FormHelperText>
                </FormControl>

                <FormControl fullWidth margin="normal">
                  <InputLabel id="driver-label">Driver</InputLabel>
                  <Select
                    labelId="driver-label"
                    name="driver"
                    value={formData.driver.username}
                    onChange={(e) => handleFormChange('driver', { username: e.target.value })}
                    label="Driver"
                    MenuProps={{
                      sx: {
                        zIndex: 10001,
                        '& .MuiPaper-root': {
                          zIndex: 10001
                        }
                      }
                    }}
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Not Assigned</em>
                    </MenuItem>
                    {drivers && drivers.length > 0 ? (
                      drivers.map((driver) => (
                        <MenuItem key={driver.username} value={driver.username}>
                          {`${driver.username} - ${driver.fullName}`.trim()}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled value="">
                        No drivers available
                      </MenuItem>
                    )}
                  </Select>
                  <FormHelperText>Driver selection is optional</FormHelperText>
                </FormControl>

                <FormControl fullWidth margin="normal">
                  <InputLabel id="delivery-vehicle-label">Delivery Vehicle</InputLabel>
                  <Select
                    labelId="delivery-vehicle-label"
                    id="deliveryVehicle"
                    name="deliveryVehicle"
                    value={vehicleTypes.some(vt => vt.name === formData.deliveryVehicle) ? formData.deliveryVehicle : ''}
                    onChange={(e) => handleInputChange({
                      target: {
                        name: 'deliveryVehicle',
                        value: e.target.value
                      }
                    } as React.ChangeEvent<HTMLInputElement>)}
                    label="Delivery Vehicle"
                    startAdornment={
                      <InputAdornment position="start">
                        <LocalShippingOutlinedIcon color="action" />
                      </InputAdornment>
                    }
                    sx={{
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Auto-assign</em>
                    </MenuItem>
                    {vehicleTypes && vehicleTypes.length > 0 ? (
                      vehicleTypes.map((vehicleType) => (
                        <MenuItem key={vehicleType.id} value={vehicleType.name}>
                          {vehicleType.name}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled>
                        {vehicleTypesLoading ? 'Loading...' : 'No vehicle types available'}
                      </MenuItem>
                    )}
                  </Select>
                  <FormHelperText sx={{ display: 'flex', alignItems: 'center' }}>
                    {vehicleTypesLoading ? 'Loading vehicle types...' : 'Will be auto-assigned based on weight, CBM, and pieces if left empty'}
                    {!vehicleTypesLoading && (
                      <Tooltip title="The system will automatically determine the appropriate vehicle type based on the manifest's weight, CBM, and number of pieces. You can manually select a different vehicle type if needed." arrow>
                        <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
                          <InfoIcon fontSize="small" color="action" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </FormHelperText>
                </FormControl>

                <TextField
                  name="weight"
                  label="Weight (kg)"
                  type="number"
                  value={formData.weight || ''}
                  onChange={(e) => handleFormChange('weight', parseFloat(e.target.value) || 0)}
                  fullWidth
                  margin="normal"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />

                <Box position="relative" width="100%" sx={{ gridColumn: '1 / -1', mt: 2 }}>
                  <SplitDeliveryDatePicker
                    label="Delivery Date & Time Slot"
                    value={formData.deliveryDate}
                    onChange={(newValue) => handleDateChange('deliveryDate', newValue)}
                    timeSlotValue={formData.timeSlot || ''}
                    onTimeSlotChange={handleTimeSlotChange}
                    fullWidth
                    slotProps={{
                      datePicker: {
                        popper: {
                          sx: {
                            zIndex: 10001,
                            '& .MuiPaper-root': {
                              zIndex: 10001
                            }
                          },
                          disablePortal: false,
                          placement: 'bottom-start'
                        },
                        textField: {
                          onFocus: (e: React.FocusEvent<HTMLInputElement>) => {
                            // Ensure the input doesn't conflict with dialog focus management
                            e.target.setAttribute('aria-describedby', 'delivery-date-helper');
                          }
                        }
                      },
                      timeSlot: {
                        MenuProps: {
                          sx: {
                            zIndex: 10001,
                            '& .MuiPaper-root': {
                              zIndex: 10001
                            }
                          },
                          disablePortal: false
                        }
                      }
                    }}
                  />
                </Box>
              </Box>



              {/* Driver Remarks Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Driver Remarks
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2, mb: 3 }}>
                <TextField
                  name="driverRemarks"
                  label="Driver Remarks"
                  value={formData.driverRemarks || ''}
                  onChange={(e) => handleFormChange('driverRemarks', e.target.value)}
                  fullWidth
                  multiline
                  rows={3}
                  margin="normal"
                  sx={{
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />
              </Box>

              {/* Remarks (CS) Section */}
              <Typography variant="h6" color="primary" sx={{ mb: 2, fontWeight: 'bold' }}>
                Remarks (CS)
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr' }, gap: 2 }}>
                <TextField
                  name="remarks"
                  label="Remarks (CS)"
                  value={formData.remarks || ''}
                  onChange={(e) => handleFormChange('remarks', e.target.value)}
                  fullWidth
                  multiline
                  rows={3}
                  margin="normal"
                  sx={{
                    gridColumn: '1 / -1',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                />
              </Box>
            </form>
          </DialogContent>

          <DialogActions sx={{ p: 3, bgcolor: 'grey.50' }}>
            <Button
              onClick={handleCloseCreateManifest}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveManifest}
              variant="contained"
              color="primary"
              disabled={formLoading}
              startIcon={formLoading ? <CircularProgress size={20} /> : <AddIcon />}
              sx={{ borderRadius: 2 }}
            >
              {formLoading ? 'Creating...' : 'Create Manifest'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>

      {/* Move width display to a non-fixed position that won't cause constant rerenders */}
      <Box sx={{ 
        position: 'absolute',
        bottom: '10px',
        right: '10px',
        zIndex: 100,
        backgroundColor: 'rgba(255,255,255,0.7)',
        padding: '4px 8px',
        borderRadius: '4px',
        fontSize: '0.7rem',
        color: 'text.secondary',
        pointerEvents: 'none',
        opacity: 0.8,
        transition: 'opacity 0.3s ease',
        '&:hover': { opacity: 0 }
      }}>
        Width: {containerWidth}px
      </Box>
    </Box>
  );
};

// Make sure to export the component as default
export default ManifestListTest; 