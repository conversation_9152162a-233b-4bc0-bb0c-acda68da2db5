# ContainerList Component

A comprehensive container management component that follows the design patterns and architecture of the ManifestList component.

## Overview

The ContainerList component provides a complete container management interface with advanced filtering, real-time updates, bulk operations, and professional UI design.

## Architecture

The component follows a modular architecture similar to ManifestList:

```
ContainerList/
├── ContainerList.tsx           # Main component with state management
├── ContainerListToolbar.tsx    # Toolbar with actions and title
├── ContainerListFilters.tsx    # Collapsible filters section
├── ContainerListGrid.tsx       # Data grid with container display
├── types.ts                    # TypeScript interfaces
├── index.ts                    # Export file
└── README.md                   # Documentation
```

## Components

### ContainerList (Main Component)
- **Purpose**: Main container component with state management and data fetching
- **Features**:
  - Container data loading and management
  - Real-time event handling
  - Filter state management
  - Selection handling
  - Back to top functionality

### ContainerListToolbar
- **Purpose**: Header toolbar with title, stats, and action buttons
- **Features**:
  - Container count display
  - Selection count indicator
  - Refresh, create, upload, and delete actions
  - Responsive design

### ContainerListFilters
- **Purpose**: Collapsible filters section for advanced filtering
- **Features**:
  - Search by container number, client, truck, etc.
  - Status filter chips with counts
  - Date range filtering with field selection
  - Client, truck, vessel/voyage, loading bay filters
  - Collapsible interface (open by default)
  - Active filter indicators

### ContainerListGrid
- **Purpose**: Data grid for displaying containers with actions
- **Features**:
  - Sortable and filterable columns
  - Status chips with color coding
  - Row selection for bulk operations
  - Action buttons (view, delete)
  - Responsive column layout
  - Professional styling

## Key Features

### 1. Advanced Filtering
- **Text Search**: Search across multiple container fields
- **Status Filtering**: Filter by container status with visual chips
- **Date Range**: Filter by various date fields (ETA, arrival, unstuff)
- **Field-Specific**: Client, truck number, vessel/voyage, loading bay
- **Real-time**: Filters apply immediately as you type

### 2. Real-time Updates
- **Event Listeners**: Responds to container CRUD operations
- **Auto Refresh**: Updates data when containers are created/updated/deleted
- **Live Counts**: Status counts update in real-time

### 3. Bulk Operations
- **Multi-Selection**: Select multiple containers for bulk actions
- **Bulk Delete**: Delete multiple containers at once
- **Selection Indicators**: Clear visual feedback for selected items

### 4. Professional UI
- **Consistent Design**: Follows ManifestList design patterns
- **Responsive Layout**: Works on desktop and mobile
- **Loading States**: Professional loading indicators
- **Error Handling**: Graceful error display and recovery

### 5. Navigation Integration
- **Container Details**: Click container number to view details
- **Back to Top**: Smooth scroll to container list title
- **Breadcrumb Support**: Integrates with app navigation

## Usage

### Basic Usage
```tsx
import ContainerList from '../components/ContainerList';

<ContainerList
  onContainerDelete={handleContainerDelete}
  onBulkDelete={handleBulkContainerDelete}
  onCreateContainer={handleOpenCreateContainer}
  onUploadContainer={handleOpenBulkUpload}
/>
```

### With ContainerManagementTest Page
```tsx
import ContainerManagementTest from '../pages/ContainerManagementTest';

// The page includes the ContainerList component with full functionality
<ContainerManagementTest />
```

## Props Interface

### ContainerList Props
```tsx
interface ContainerListProps {
  onContainerDelete?: (containerNo: string) => void;
  onBulkDelete?: (containerNos: string[]) => void;
  onCreateContainer?: () => void;
  onUploadContainer?: () => void;
}
```

## State Management

### Filter State
```tsx
interface ContainerListFilters {
  search: string;
  status: ContainerStatus[];
  dateRange: { start: Date | null; end: Date | null; };
  dateFieldType: 'etaRequestedDate' | 'etaAllocated' | 'arrivalDate' | 'unstuffDate';
  client: string;
  truckNo: string;
  vesselVoyage: string;
  loadingBay: string;
}
```

### Loading States
```tsx
interface ContainerListLoadingStates {
  clientsLoading?: boolean;
  containersLoading?: boolean;
}
```

## Styling

The component uses Material-UI with consistent theming:
- **Primary Colors**: Blue theme matching app design
- **Status Colors**: Color-coded status chips
- **Hover Effects**: Subtle interactions and feedback
- **Responsive Design**: Mobile-friendly layout
- **Professional Typography**: Consistent font sizes and weights

## Real-time Events

The component listens for these custom events:
- `container-created`: Refresh data when container is created
- `container-updated`: Refresh data when container is updated
- `container-deleted`: Refresh data when container is deleted
- `containers-bulk-deleted`: Refresh data after bulk deletion

## Performance Optimizations

- **Memoized Calculations**: Totals and filtered data are memoized
- **Debounced Search**: Search input is debounced to reduce API calls
- **Efficient Filtering**: Client-side filtering for better performance
- **Lazy Loading**: Components load only when needed

## Integration with ContainerManagementTest

The ContainerManagementTest page provides:
- **Container Creation Dialog**: Professional form for new containers
- **Bulk Upload Dialog**: Excel import functionality
- **Event Handling**: Proper event emission for real-time updates
- **Error Handling**: Comprehensive error management
- **Loading States**: Professional loading indicators

This creates a complete container management solution that matches the quality and functionality of the ManifestListTest page.
