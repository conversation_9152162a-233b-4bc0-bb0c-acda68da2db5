import React, { useState } from 'react';
import { 
  Box, 
  TextField, 
  InputAdornment, 
  Button, 
  Chip, 
  Typography, 
  Paper,
  Autocomplete,
  IconButton,
  Stack,
  ButtonGroup,
  Divider,
  Tooltip,
  ToggleButtonGroup,
  ToggleButton,
  Collapse
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearAllIcon from '@mui/icons-material/ClearAll';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ContainerStatus } from '../../types/Container';
import { Client } from '../../types/User';
import { ContainerListFilters as FiltersType, ContainerListTotals, ContainerListLoadingStates } from './types';

interface ContainerListFiltersProps {
  filters: FiltersType;
  onFilterChange: (filterKey: keyof FiltersType, value: any) => void;
  onResetFilters: () => void;
  clients: Client[];
  loading: ContainerListLoadingStates;
  totals: ContainerListTotals;
}

const ContainerListFilters: React.FC<ContainerListFiltersProps> = ({
  filters,
  onFilterChange,
  onResetFilters,
  clients,
  loading = {},
  totals
}) => {
  const { clientsLoading = false } = loading;
  
  // State for collapsible filters - open by default
  const [filtersExpanded, setFiltersExpanded] = useState(true);
  
  // Date field type options for autocomplete
  const dateFieldOptions = [
    { name: 'ETA Requested Date' },
    { name: 'ETA Allocated Date' },
    { name: 'Arrival Date' },
    { name: 'Unstuff Date' }
  ];
  
  // Helper to convert display name to field type
  const getFieldTypeFromName = (name: string): 'etaRequestedDate' | 'etaAllocated' | 'arrivalDate' | 'unstuffDate' => {
    switch (name) {
      case 'ETA Allocated Date': return 'etaAllocated';
      case 'Arrival Date': return 'arrivalDate';
      case 'Unstuff Date': return 'unstuffDate';
      default: return 'etaRequestedDate';
    }
  };
  
  // Helper to convert field type to display name
  const getNameFromFieldType = (fieldType: string): string => {
    switch (fieldType) {
      case 'etaAllocated': return 'ETA Allocated Date';
      case 'arrivalDate': return 'Arrival Date';
      case 'unstuffDate': return 'Unstuff Date';
      default: return 'ETA Requested Date';
    }
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange('search', event.target.value);
  };

  // Handle status filter toggle
  const handleStatusToggle = (status: ContainerStatus) => {
    const currentStatuses = filters.status;
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status];
    onFilterChange('status', newStatuses);
  };

  // Handle date range changes
  const handleDateRangeChange = (field: 'start' | 'end', value: Date | null) => {
    onFilterChange('dateRange', {
      ...filters.dateRange,
      [field]: value
    });
  };

  // Handle date field type change
  const handleDateFieldTypeChange = (event: React.SyntheticEvent, value: { name: string } | null) => {
    onFilterChange('dateFieldType', value ? getFieldTypeFromName(value.name) : 'etaRequestedDate');
  };

  // Handle client filter change
  const handleClientChange = (event: React.SyntheticEvent, value: { companyName: string; username: string } | null) => {
    onFilterChange('client', value ? value.username : '');
  };

  // Get container status colors and icons
  const getStatusChipProps = (status: ContainerStatus) => {
    const isSelected = filters.status.includes(status);
    const count = totals.byStatus[status] || 0;
    
    const baseProps = {
      size: 'small' as const,
      clickable: true,
      onClick: () => handleStatusToggle(status),
      sx: { 
        fontSize: '0.7rem', 
        height: '24px',
        transition: 'all 0.2s ease',
        '&:hover': {
          transform: 'scale(1.05)',
        }
      }
    };

    if (isSelected) {
      return {
        ...baseProps,
        color: 'primary' as const,
        variant: 'filled' as const
      };
    }

    return {
      ...baseProps,
      variant: 'outlined' as const,
      sx: {
        ...baseProps.sx,
        opacity: count > 0 ? 1 : 0.5
      }
    };
  };

  return (
    <Paper sx={{
      mb: 0,
      borderRadius: 0,
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
      overflow: 'visible',
      position: 'relative',
      zIndex: 1,
    }}>
      {/* Collapsible header */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 1.5,
        py: 0.75,
        borderBottom: filtersExpanded ? '1px solid rgba(224, 224, 224, 0.5)' : 'none',
        cursor: 'pointer',
        minHeight: '36px',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.02)'
        }
      }} onClick={() => setFiltersExpanded(!filtersExpanded)}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
          <FilterListIcon sx={{ fontSize: '16px' }} color="primary" />
          <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main', fontSize: '0.8rem' }}>
            Filters
          </Typography>
          {/* Show active filter count */}
          {(filters.search || filters.status.length > 0 || filters.dateRange.start ||
            filters.dateRange.end || filters.client || filters.truckNo ||
            filters.vesselVoyage || filters.loadingBay) && (
            <Chip 
              size="small" 
              label="Active" 
              color="primary"
              sx={{ height: '16px', fontSize: '0.65rem', '& .MuiChip-label': { px: 0.5 } }}
            />
          )}
        </Box>
        <IconButton 
          size="small" 
          sx={{ 
            p: 0.25,
            width: '24px',
            height: '24px',
            transition: 'transform 0.2s ease',
            transform: filtersExpanded ? 'rotate(0deg)' : 'rotate(0deg)'
          }}
        >
          {filtersExpanded ? <ExpandLessIcon sx={{ fontSize: '16px' }} /> : <ExpandMoreIcon sx={{ fontSize: '16px' }} />}
        </IconButton>
      </Box>

      {/* Collapsible filters content */}
      <Collapse in={filtersExpanded} timeout={300}>
        <Box sx={{ p: 1.5, pt: 1 }}>
          {/* Main container for all filters with grid layout */}
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(12, 1fr)', gap: 1 }}>
            
            {/* Search input - spans 4 columns */}
            <Box sx={{ gridColumn: 'span 4' }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search containers..."
                value={filters.search}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                  sx: { fontSize: '0.75rem' }
                }}
                InputLabelProps={{
                  sx: { fontSize: '0.75rem' }
                }}
              />
            </Box>

            {/* Client filter - spans 3 columns */}
            <Box sx={{ gridColumn: 'span 3' }}>
              <Autocomplete
                size="small"
                options={clients}
                getOptionLabel={(option) => option.companyName || option.username}
                value={clients.find(client => client.username === filters.client) || null}
                onChange={handleClientChange}
                loading={clientsLoading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Client"
                    fullWidth
                    size="small"
                    variant="outlined"
                    sx={{ minHeight: '32px' }}
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        sx: { fontSize: '0.75rem' }
                      },
                      inputLabel: {
                        ...params.InputLabelProps,
                        sx: { fontSize: '0.75rem' }
                      }
                    }}
                  />
                )}
              />
            </Box>

            {/* Truck No filter - spans 2 columns */}
            <Box sx={{ gridColumn: 'span 2' }}>
              <TextField
                fullWidth
                size="small"
                label="Truck No"
                value={filters.truckNo}
                onChange={(e) => onFilterChange('truckNo', e.target.value)}
                InputProps={{
                  sx: { fontSize: '0.75rem' }
                }}
                InputLabelProps={{
                  sx: { fontSize: '0.75rem' }
                }}
              />
            </Box>

            {/* Vessel/Voyage filter - spans 3 columns */}
            <Box sx={{ gridColumn: 'span 3' }}>
              <TextField
                fullWidth
                size="small"
                label="Vessel/Voyage"
                value={filters.vesselVoyage}
                onChange={(e) => onFilterChange('vesselVoyage', e.target.value)}
                InputProps={{
                  sx: { fontSize: '0.75rem' }
                }}
                InputLabelProps={{
                  sx: { fontSize: '0.75rem' }
                }}
              />
            </Box>

            {/* Date field type selector - spans 2 columns */}
            <Box sx={{ gridColumn: 'span 2' }}>
              <Autocomplete
                size="small"
                options={dateFieldOptions}
                getOptionLabel={(option) => option.name}
                value={dateFieldOptions.find(option => option.name === getNameFromFieldType(filters.dateFieldType || 'etaRequestedDate')) || null}
                onChange={handleDateFieldTypeChange}
                loading={false}
                slotProps={{
                  popper: {
                    sx: {
                      '& .MuiAutocomplete-listbox': {
                        fontSize: '0.75rem'
                      }
                    },
                    modifiers: [
                      {
                        name: 'flip',
                        enabled: false,
                      },
                      {
                        name: 'preventOverflow',
                        enabled: true,
                        options: {
                          altAxis: true,
                          altBoundary: true,
                          tether: true,
                          rootBoundary: 'document',
                        },
                      },
                    ],
                  }
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Filter by Date Field"
                    fullWidth
                    size="small"
                    variant="outlined"
                    sx={{ minHeight: '32px' }}
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        sx: { fontSize: '0.75rem'}
                      },
                      inputLabel: {
                        ...params.InputLabelProps,
                        sx: { fontSize: '0.75rem' }
                      }
                    }}
                  />
                )}
              />
            </Box>

            {/* Date range filters - spans 5 columns */}
            <Box sx={{ gridColumn: 'span 5', display: 'flex', gap: 1, alignItems: 'center' }}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Start Date"
                  value={filters.dateRange.start}
                  onChange={(value) => handleDateRangeChange('start', value)}
                  format="dd MMM yyyy"
                  slotProps={{
                    textField: {
                      size: 'small',
                      sx: { 
                        flex: 1,
                        '& .MuiInputBase-input': { fontSize: '0.75rem' },
                        '& .MuiInputLabel-root': { fontSize: '0.75rem' }
                      }
                    },
                    popper: {
                      sx: { zIndex: 1 }
                    }
                  }}
                />
                <DatePicker
                  label="End Date"
                  value={filters.dateRange.end}
                  onChange={(value) => handleDateRangeChange('end', value)}
                  format="dd MMM yyyy"
                  slotProps={{
                    textField: {
                      size: 'small',
                      sx: { 
                        flex: 1,
                        '& .MuiInputBase-input': { fontSize: '0.75rem' },
                        '& .MuiInputLabel-root': { fontSize: '0.75rem' }
                      }
                    },
                    popper: {
                      sx: { zIndex: 1 }
                    }
                  }}
                />
              </LocalizationProvider>
            </Box>

            {/* Loading Bay filter - spans 3 columns */}
            <Box sx={{ gridColumn: 'span 3' }}>
              <TextField
                fullWidth
                size="small"
                label="Loading Bay"
                value={filters.loadingBay}
                onChange={(e) => onFilterChange('loadingBay', e.target.value)}
                InputProps={{
                  sx: { fontSize: '0.75rem' }
                }}
                InputLabelProps={{
                  sx: { fontSize: '0.75rem' }
                }}
              />
            </Box>

            {/* Status filters - spans 12 columns */}
            <Box sx={{ gridColumn: 'span 12' }}>
              <Box sx={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: 0.5,
                maxHeight: '120px',
                overflowY: 'auto',
                overflowX: 'hidden',
                '&::-webkit-scrollbar': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                  borderRadius: '3px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#c1c1c1',
                  borderRadius: '3px',
                  '&:hover': {
                    background: '#a8a8a8',
                  },
                },
              }}>
                {Object.values(ContainerStatus).map((status) => {
                  const count = totals.byStatus[status] || 0;
                  const chipProps = getStatusChipProps(status);
                  
                  return (
                    <Chip
                      key={status}
                      label={`${status.replace(/_/g, ' ')} (${count})`}
                      {...chipProps}
                    />
                  );
                })}
              </Box>
            </Box>

            {/* Reset filters button */}
            {(filters.search || filters.status.length > 0 || filters.dateRange.start ||
              filters.dateRange.end || filters.client || filters.truckNo ||
              filters.vesselVoyage || filters.loadingBay) && (
              <Box sx={{ mt: 0.5, display: 'flex', justifyContent: 'flex-start' }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={onResetFilters}
                  startIcon={<ClearAllIcon />}
                  color="error"
                  sx={{
                    borderRadius: 2,
                    fontSize: '0.75rem',
                    py: 0.25,
                    px: 1
                  }}
                >
                  Clear All Filters
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </Collapse>
    </Paper>
  );
};

export default ContainerListFilters;
