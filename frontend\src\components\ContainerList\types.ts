import { ContainerStatus } from '../../types/Container';

// Container list filters interface
export interface ContainerListFilters {
  search: string;
  status: ContainerStatus[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  dateFieldType: 'etaRequestedDate' | 'etaAllocated' | 'arrivalDate' | 'unstuffDate';
  client: string;
  truckNo: string;
  vesselVoyage: string;
  loadingBay: string;
}

// Loading states for various resources
export interface ContainerListLoadingStates {
  clientsLoading?: boolean;
  containersLoading?: boolean;
}

// Totals and statistics
export interface ContainerListTotals {
  total: number;
  filtered: number;
  selected: number;
  byStatus: Record<ContainerStatus, number>;
}

// Column visibility settings
export interface ContainerColumnVisibility {
  containerNo: boolean;
  status: boolean;
  clientUsername: boolean;
  truckNo: boolean;
  vesselVoyageNo: boolean;
  manifestQuantity: boolean;
  etaRequestedDate: boolean;
  etaAllocated: boolean;
  arrivalDate: boolean;
  loadingBay: boolean;
  unstuffDate: boolean;
  unstuffCompletedDate: boolean;
  pullOutDate: boolean;
  unstuffTeam: boolean;
  remark: boolean;
  actions: boolean;
}

// Event types for real-time updates
export interface ContainerEventDetail {
  type: 'created' | 'updated' | 'deleted' | 'bulk-deleted';
  containerNo?: string;
  containerNos?: string[];
  container?: any;
  timestamp: string;
}

// Form data for container creation/editing
export interface ContainerFormData {
  containerNo: string;
  clientUsername: string;
  truckNo: string;
  vesselVoyageNo: string;
  etaRequestedDate: Date | null;
  manifestQuantity: number;
  portnetEta: Date | null;
  etaAllocated: Date | null;
  arrivalDate: Date | null;
  loadingBay: string;
  unstuffDate: Date | null;
  unstuffCompletedDate: Date | null;
  pullOutDate: Date | null;
  unstuffTeam: string;
  remark: string;
  status: ContainerStatus;
}
