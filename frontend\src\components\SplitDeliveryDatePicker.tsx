import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  SelectChangeEvent,
  IconButton,
  Tooltip,
  InputAdornment,
} from '@mui/material';
import ClearIcon from '@mui/icons-material/Clear';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { MobileDatePicker } from '@mui/x-date-pickers/MobileDatePicker';
import { useTheme, useMediaQuery } from '@mui/material';
import { TimeSlot, TIME_SLOTS, getTimeSlotIdFromDate } from '../utils/dateUtils';

// No need to redefine the TimeSlot interface and TIME_SLOTS here since we import them

interface SplitDeliveryDatePickerProps {
  label?: string;
  value: Date | null;
  onChange: (date: Date | null) => void;
  onTimeSlotChange: (timeSlotId: string | null) => void; // Required callback for time slot changes
  timeSlotValue: string | null; // Required time slot value
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  fullWidth?: boolean;
  sx?: any;
  slotProps?: {
    datePicker?: any;
    timeSlot?: any;
  };
}

const SplitDeliveryDatePicker: React.FC<SplitDeliveryDatePickerProps> = ({
  label = "Delivery Date & Time",
  value,
  onChange,
  onTimeSlotChange,
  timeSlotValue,
  error = false,
  helperText,
  disabled = false,
  fullWidth = true,
  sx,
  slotProps = {}
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>('');

  // Extract date and time slot from the incoming values
  useEffect(() => {
    if (value) {
      // Just use the date part, ignoring the time
      setSelectedDate(new Date(value.getFullYear(), value.getMonth(), value.getDate()));
    } else {
      setSelectedDate(null);
    }
    
    // Use the separate timeSlotValue prop
    if (timeSlotValue) {
      setSelectedTimeSlot(timeSlotValue);
    } else {
      setSelectedTimeSlot('');
    }
  }, [value, timeSlotValue]);

  // Handle date change
  const handleDateChange = (newDate: Date | null) => {
    setSelectedDate(newDate);
    onChange(newDate); // Pass the date without time component
  };

  // Handle clear date
  const handleClearDate = () => {
    setSelectedDate(null);
    setSelectedTimeSlot('');
    onChange(null);
    onTimeSlotChange(null);
  };

  // Handle time slot change
  const handleTimeSlotChange = (event: SelectChangeEvent<string>) => {
    const newTimeSlot = event.target.value;
    setSelectedTimeSlot(newTimeSlot);
    
    // Call the separate time slot callback
    onTimeSlotChange(newTimeSlot || null);
  };

  return (
    <Box sx={{ ...sx }}>
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500 }}>
        {label}
      </Typography>
      
      <Box sx={{
        display: 'flex',
        gap: 2,
        flexDirection: 'column', // Always use column layout to prevent overlap
        width: fullWidth ? '100%' : 'auto',
        position: 'relative',
        zIndex: 1
      }}>
        {/* Date Picker */}
        <Box sx={{ width: '100%', position: 'relative', mb: 1 }}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            {isMobile ? (
              <MobileDatePicker
                label="Date"
                value={selectedDate}
                onChange={handleDateChange}
                disabled={disabled}
                format="dd MMM yyyy"
                views={['year', 'month', 'day']}
                openTo="day"
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: error,
                    size: "medium",
                    InputProps: {
                      endAdornment: selectedDate ? (
                        <InputAdornment position="end">
                          <Tooltip
                            title="Clear delivery date"
                            slotProps={{
                              tooltip: {
                                sx: {
                                  zIndex: 10003
                                }
                              }
                            }}
                          >
                            <IconButton
                              size="small"
                              onClick={handleClearDate}
                              edge="end"
                              sx={{
                                color: 'text.secondary',
                                '&:hover': { color: 'error.main' }
                              }}
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </InputAdornment>
                      ) : null,
                    },
                    ...slotProps.datePicker?.textField
                  },
                  dialog: {
                    sx: {
                      zIndex: 10001, // Higher than desktop popper
                      '& .MuiPickersLayout-root': {
                        overflow: 'visible'
                      },
                      '& .MuiDialogContent-root': {
                        overflow: 'visible'
                      }
                    }
                  },
                  ...slotProps.datePicker
                }}
              />
            ) : (
              <DatePicker
                label="Date"
                value={selectedDate}
                onChange={handleDateChange}
                disabled={disabled}
                format="dd MMM yyyy"
                views={['year', 'month', 'day']}
                openTo="day"
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: error,
                    size: "medium",
                    InputProps: {
                      endAdornment: selectedDate ? (
                        <InputAdornment position="end">
                          <Tooltip
                            title="Clear delivery date"
                            slotProps={{
                              tooltip: {
                                sx: {
                                  zIndex: 10003
                                }
                              }
                            }}
                          >
                            <IconButton
                              size="small"
                              onClick={handleClearDate}
                              edge="end"
                              sx={{
                                color: 'text.secondary',
                                '&:hover': { color: 'error.main' }
                              }}
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </InputAdornment>
                      ) : null,
                    },
                    ...slotProps.datePicker?.textField
                  },
                  popper: {
                    sx: {
                      zIndex: 10000,
                      '& .MuiPaper-root': {
                        maxWidth: '260px',
                        maxHeight: '280px',
                        overflow: 'auto'
                      }
                    },
                    disablePortal: false, // Use false for better positioning
                    placement: "bottom-start",
                    ...slotProps.datePicker?.popper
                  },
                  ...slotProps.datePicker
                }}
              />
            )}
              </LocalizationProvider>
            </Box>

            {/* Clear Button */}
            {selectedDate && (
              <Tooltip
                title="Clear delivery date"
                componentsProps={{
                  tooltip: {
                    sx: {
                      zIndex: 10003 // Higher than date picker dialog
                    }
                  }
                }}
              >
                <IconButton
                  size="small"
                  onClick={handleClearDate}
                  sx={{
                    color: 'text.secondary',
                    '&:hover': { color: 'error.main' }
                  }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Time Slot Dropdown */}
        <Box sx={{ width: '100%' }}>
          <FormControl fullWidth error={error} disabled={disabled || !selectedDate}>
            <InputLabel id="time-slot-label">Time Slot</InputLabel>
            <Select
              labelId="time-slot-label"
              value={selectedTimeSlot}
              onChange={handleTimeSlotChange}
              label="Time Slot"
              size="medium"
              {...slotProps.timeSlot}
            >
              {TIME_SLOTS.map((slot) => (
                <MenuItem key={slot.id} value={slot.id}>
                  {slot.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      {helperText && (
        <Typography 
          variant="caption" 
          color={error ? 'error' : 'text.secondary'}
          sx={{ mt: 1, display: 'block' }}
        >
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default SplitDeliveryDatePicker; 