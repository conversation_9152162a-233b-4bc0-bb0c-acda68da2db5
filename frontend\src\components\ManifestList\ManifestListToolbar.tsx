import React, { forwardRef } from 'react';
import { Box, Button, Tooltip, Typography, IconButton, useTheme, Chip } from '@mui/material';
// import PrintIcon from '@mui/icons-material/Print'; // Removed - Generate Labels button hidden
import DeleteIcon from '@mui/icons-material/Delete';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import RefreshIcon from '@mui/icons-material/Refresh';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AddIcon from '@mui/icons-material/Add';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

import { Manifest } from '../../types/manifest';

interface ManifestListToolbarProps {
  selectedManifests: Manifest[];
  totalManifests: number;
  onExport: () => void;
  onExportForRTrack: () => void; // New prop for RTrack export
  onPrintLabels: () => void;
  onDelete: () => void;
  onRefresh: () => void;
  onBulkDeliveryDateUpdate: () => void;
  isContainerView: boolean;
  onCreateManifest?: () => void;
  onUploadManifest?: () => void;
}

const ManifestListToolbar = forwardRef<HTMLDivElement, ManifestListToolbarProps>(({
  selectedManifests,
  totalManifests,
  onExport,
  onExportForRTrack,
  onPrintLabels,
  onDelete,
  onRefresh,
  onBulkDeliveryDateUpdate,
  isContainerView,
  onCreateManifest,
  onUploadManifest
}, ref) => {
  const hasSelection = selectedManifests.length > 0;
  const theme = useTheme();
  
  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column',
      borderBottom: '1px solid rgba(224, 224, 224, 1)',
      bgcolor: '#ffffff',
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
    }}>
      {/* First row with title and refresh button */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pl: { sm: 2 },
        pr: { xs: 1, sm: 1 },
        py: 1,
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1.5 }}>
          <Typography
            ref={ref}
            variant="h6"
            component="div"
            sx={{ mr: 1 }}
          >
            Manifest List
          </Typography>
          <Chip 
            label={`Total: ${totalManifests}`} 
            size="small" 
            color="primary" 
            variant="outlined"
            sx={{ 
              height: '22px', 
              fontSize: '0.75rem',
              fontWeight: 500,
              '& .MuiChip-label': { px: 1 }
            }} 
          />
          
          <Tooltip title="Refresh data">
            <IconButton 
              size="small" 
              onClick={onRefresh}
              sx={{
                color: theme.palette.primary.main,
                '&:hover': {
                  bgcolor: 'rgba(25, 118, 210, 0.08)',
                }
              }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title={hasSelection ? `Update delivery date for ${selectedManifests.length} selected manifest(s)` : "Select manifests to update delivery date"}>
            <span>
              <Button
                variant={hasSelection ? "contained" : "outlined"}
                color="success"
                startIcon={<CalendarTodayIcon />}
                size="small"
                onClick={onBulkDeliveryDateUpdate}
                disabled={!hasSelection}
                sx={{
                  borderRadius: 2,
                  minWidth: 'auto',
                  px: 2, // Consistent padding
                  py: 0.5,
                  fontSize: '0.75rem',
                  whiteSpace: 'nowrap',
                  fontWeight: 'bold', // Consistent font weight
                  boxShadow: hasSelection ? 2 : 0,
                }}
              >
                Update Delivery Date
              </Button>
            </span>
          </Tooltip>
          
          <Tooltip title={hasSelection ? `Delete ${selectedManifests.length} selected manifest(s)` : "Select manifests to delete"}>
            <span>
              <Button
                variant={hasSelection ? "contained" : "outlined"}
                color="error"
                startIcon={<DeleteIcon />}
                size="small"
                onClick={onDelete}
                disabled={!hasSelection}
                sx={{
                  borderRadius: 2,
                  minWidth: 'auto',
                  px: 2, // Consistent padding
                  py: 0.5,
                  fontSize: '0.75rem',
                  whiteSpace: 'nowrap',
                  fontWeight: 'bold', // Consistent font weight
                  boxShadow: hasSelection ? 2 : 0,
                }}
              >
                Delete Selected
              </Button>
            </span>
          </Tooltip>
          
          <Button
            variant="outlined"
            color="info"
            startIcon={<FileDownloadIcon />}
            size="small"
            onClick={onExport}
            sx={{
              borderRadius: 2,
              minWidth: 'auto',
              px: 2, // Consistent padding
              py: 0.5,
              fontSize: '0.75rem',
              fontWeight: 'bold', // Consistent font weight
              whiteSpace: 'nowrap',
              backgroundColor: theme.palette.info.main,
              color: theme.palette.common.white,
              '&:hover': {
                backgroundColor: theme.palette.info.dark,
              },
            }}
          >
            Export
          </Button>

          {/* Export For RTrack Button */}
          <Button
            variant="outlined"
            color="info"
            startIcon={<FileDownloadIcon />}
            size="small"
            onClick={onExportForRTrack}
            sx={{
              borderRadius: 2,
              minWidth: 'auto',
              px: 2, // Consistent padding
              py: 0.5,
              fontSize: '0.75rem',
              fontWeight: 'bold', // Consistent font weight
              whiteSpace: 'nowrap',
              backgroundColor: theme.palette.info.main,
              color: theme.palette.common.white,
              '&:hover': {
                backgroundColor: theme.palette.info.dark,
              },
            }}
          >
            Export For RTrack
          </Button>

          {/* Action buttons to the right of export */}
          {(onCreateManifest || onUploadManifest) && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              {onCreateManifest && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={onCreateManifest}
                  color="success"
                  size="small"
                  sx={{
                    fontWeight: 'bold',
                    borderRadius: 2,
                    boxShadow: 2,
                    px: 2,
                    py: 0.5,
                    fontSize: '0.75rem',
                    '&:hover': { boxShadow: 4 }
                  }}
                >
                  Create Manifest
                </Button>
              )}
              {onUploadManifest && (
                <Button
                  variant="contained"
                  startIcon={<CloudUploadIcon />}
                  onClick={onUploadManifest}
                  color="primary"
                  size="small"
                  sx={{
                    fontWeight: 'bold',
                    borderRadius: 2,
                    boxShadow: 2,
                    px: 2,
                    py: 0.5,
                    fontSize: '0.75rem',
                    '&:hover': { boxShadow: 4 }
                  }}
                >
                  Upload Manifest
                </Button>
              )}

            </Box>
          )}

          {hasSelection && (
            <Typography variant="caption">
              {selectedManifests.length} manifest{selectedManifests.length !== 1 ? 's' : ''} selected
            </Typography>
          )}
        </Box>
        
        {/* Empty Box to maintain layout structure */}
        <Box></Box>
      </Box>
      
      {/* Remove the second row with action buttons */}
    </Box>
  );
});

export default ManifestListToolbar; 