import React, { useEffect, useState, useRef, useCallback, Suspense } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Button,
  <PERSON>ack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  LinearProgress,
  Divider,
  Paper,
  IconButton,
  Chip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Collapse,
  Autocomplete,
  InputAdornment,
  FormHelperText,
  SelectChangeEvent,
  Tooltip
} from '@mui/material';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import TimelineIcon from '@mui/icons-material/Timeline';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import InfoIcon from '@mui/icons-material/Info';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import GetAppIcon from '@mui/icons-material/GetApp';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import AddIcon from '@mui/icons-material/Add';
import ContainerList from '../components/ContainerList';
import usePageTitle from '../hooks/usePageTitle';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../contexts/ToastContext';
import CustomDatePickerWithCounts from '../components/CustomDatePickerWithCounts';
import containerService from '../services/container.service';
import userService from '../services/user.service';
import { Container, ContainerStatus } from '../types/Container';
import { Client } from '../types/User';
import { ContainerFormData } from '../components/ContainerList/types';
import Excel from 'exceljs';

// Debounce helper function
const debounce = (fn: Function, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function(...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), ms);
  };
};

// Loading fallback component
const LoadingFallback = () => (
  <Box sx={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center', 
    height: '300px',
    width: '100%'
  }}>
    <CircularProgress size={40} />
    <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
      Loading container list...
    </Typography>
  </Box>
);

/**
 * ContainerManagementTest Page Component
 * 
 * This page provides a comprehensive container management interface with:
 * - Container listing with advanced filtering and search
 * - Container creation and editing capabilities
 * - Bulk operations and Excel import/export
 * - Real-time updates and status management
 * - Professional UI following ManifestListTest design patterns
 */
const ContainerManagementTest: React.FC = () => {
  usePageTitle('Container Management');
  
  const { currentUser } = useAuth();
  const toast = useToast();
  
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const firstInputRef = useRef<HTMLInputElement>(null);
  
  // State management
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [resourcesLoading, setResourcesLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  
  // Dialog states
  const [createContainerDialogOpen, setCreateContainerDialogOpen] = useState(false);
  const [bulkUploadDialogOpen, setBulkUploadDialogOpen] = useState(false);
  
  // Form states
  const [formData, setFormData] = useState<ContainerFormData>({
    containerNo: '',
    clientUsername: '',
    truckNo: '',
    vesselVoyageNo: '',
    etaRequestedDate: null,
    manifestQuantity: 0,
    portnetEta: null,
    etaAllocated: null,
    arrivalDate: null,
    loadingBay: '',
    unstuffDate: null,
    unstuffCompletedDate: null,
    pullOutDate: null,
    unstuffTeam: '',
    remark: '',
    status: ContainerStatus.CREATED
  });
  
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  
  // Bulk upload states
  const [activeStep, setActiveStep] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<any[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  // Width update function with debounce
  const updateWidth = useCallback(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, []);
  
  const debouncedUpdateWidth = useCallback(debounce(updateWidth, 100), [updateWidth]);
  
  // Load clients
  const loadClients = async () => {
    try {
      setResourcesLoading(true);
      const response = await userService.getClients(currentUser?.token || '');
      if (response.success && response.data) {
        setClients(response.data);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
      toast.error('Failed to load clients');
    } finally {
      setResourcesLoading(false);
    }
  };
  
  // Container management handlers
  const handleOpenCreateContainer = () => {
    setCreateContainerDialogOpen(true);
    setFormError(null);
  };
  
  const handleCloseCreateContainer = () => {
    setCreateContainerDialogOpen(false);
    setFormData({
      containerNo: '',
      clientUsername: '',
      truckNo: '',
      vesselVoyageNo: '',
      etaRequestedDate: null,
      manifestQuantity: 0,
      portnetEta: null,
      etaAllocated: null,
      arrivalDate: null,
      loadingBay: '',
      unstuffDate: null,
      unstuffCompletedDate: null,
      pullOutDate: null,
      unstuffTeam: '',
      remark: '',
      status: ContainerStatus.CREATED
    });
    setFormError(null);
  };
  
  const handleOpenBulkUpload = () => {
    setBulkUploadDialogOpen(true);
    setActiveStep(0);
    setSelectedFile(null);
    setUploadProgress(0);
    setUploadResults([]);
    setUploadError(null);
  };
  
  // Container deletion handlers
  const handleContainerDelete = async (containerNo: string) => {
    try {
      const response = await containerService.deleteContainer(containerNo, currentUser?.token || '');
      if (response.success) {
        toast.success('Container deleted successfully');

        // Emit event for real-time updates
        const containerDeletedEvent = new CustomEvent('container-deleted', {
          detail: {
            type: 'deleted',
            containerNo: containerNo,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(containerDeletedEvent);
      } else {
        toast.error(response.message || 'Failed to delete container');
      }
    } catch (error: any) {
      console.error('Error deleting container:', error);
      toast.error(error.response?.data?.message || 'Failed to delete container');
    }
  };
  
  const handleBulkContainerDelete = async (containerNos: string[]) => {
    try {
      // For now, simulate bulk delete since we may not have the service method
      for (const containerNo of containerNos) {
        await containerService.deleteContainer(containerNo, currentUser?.token || '');
      }

      toast.success(`${containerNos.length} containers deleted successfully`);

      // Emit event for real-time updates
      const bulkDeleteEvent = new CustomEvent('containers-bulk-deleted', {
        detail: {
          type: 'bulk-deleted',
          containerNos: containerNos,
          timestamp: new Date().toISOString()
        }
      });
      window.dispatchEvent(bulkDeleteEvent);
    } catch (error: any) {
      console.error('Error bulk deleting containers:', error);
      toast.error(error.response?.data?.message || 'Failed to delete containers');
    }
  };

  // Handle container creation
  const handleCreateContainer = async () => {
    try {
      setFormLoading(true);
      setFormError(null);

      // Find the selected client
      const selectedClient = clients.find(c => c.username === formData.clientUsername);
      if (!selectedClient) {
        setFormError('Please select a valid client');
        return;
      }

      // Transform form data to Container format
      const containerData: Container = {
        containerNo: formData.containerNo,
        client: selectedClient,
        truckNo: formData.truckNo,
        vesselVoyageNo: formData.vesselVoyageNo,
        etaRequestedDate: formData.etaRequestedDate?.toISOString() || '',
        manifestQuantity: formData.manifestQuantity,
        portnetEta: formData.portnetEta?.toISOString(),
        etaAllocated: formData.etaAllocated?.toISOString(),
        arrivalDate: formData.arrivalDate?.toISOString(),
        loadingBay: formData.loadingBay,
        unstuffDate: formData.unstuffDate?.toISOString(),
        unstuffCompletedDate: formData.unstuffCompletedDate?.toISOString(),
        pullOutDate: formData.pullOutDate?.toISOString(),
        unstuffTeam: formData.unstuffTeam,
        remark: formData.remark,
        status: formData.status
      };

      const response = await containerService.createContainer(containerData, currentUser?.token || '');
      if (response.success) {
        toast.success('Container created successfully');
        handleCloseCreateContainer();

        // Emit event for real-time updates
        const containerCreatedEvent = new CustomEvent('container-created', {
          detail: {
            type: 'created',
            container: response.data,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(containerCreatedEvent);
      } else {
        setFormError(response.message || 'Failed to create container');
      }
    } catch (error: any) {
      console.error('Error creating container:', error);
      setFormError(error.response?.data?.message || 'Failed to create container');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle form field changes
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle client selection change
  const handleClientChange = (clientUsername: string) => {
    setFormData(prev => ({
      ...prev,
      clientUsername: clientUsername,
      // Reset dependent fields when client changes
      containerNo: '',
      truckNo: '',
      vesselVoyageNo: ''
    }));
  };
  
  // Load resources on component mount
  useEffect(() => {
    console.log('Loading resources on component mount, currentUser:', currentUser?.username);
    loadClients();
  }, [currentUser]);
  
  // Focus management for dialog
  useEffect(() => {
    if (createContainerDialogOpen && firstInputRef.current) {
      setTimeout(() => {
        const rootElement = document.getElementById('root');
        if (rootElement && rootElement.getAttribute('aria-hidden') === 'true') {
          rootElement.removeAttribute('aria-hidden');
        }
        firstInputRef.current?.focus();
      }, 150);
    }
  }, [createContainerDialogOpen]);
  
  // Update width on resize
  useEffect(() => {
    const initialTimer = setTimeout(() => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    }, 500);
    
    window.addEventListener('resize', debouncedUpdateWidth);
    
    const observer = new MutationObserver(debouncedUpdateWidth);
    if (containerRef.current) {
      observer.observe(document.body, { 
        childList: false,
        subtree: false,
        attributes: true,
        attributeFilter: ['class', 'style']
      });
    }
    
    return () => {
      window.removeEventListener('resize', debouncedUpdateWidth);
      observer.disconnect();
      clearTimeout(initialTimer);
    };
  }, [debouncedUpdateWidth]);
  
  return (
    <Box 
      ref={containerRef}
      sx={{ 
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 0
      }}
    >
      {/* The ContainerList component will fetch data from the API */}
      <Box sx={{ 
        flexGrow: 1, 
        width: '100%',
        px: 0
      }}>
        <Suspense fallback={<LoadingFallback />}>
          <ContainerList
            onContainerDelete={handleContainerDelete}
            onBulkDelete={handleBulkContainerDelete}
            onCreateContainer={handleOpenCreateContainer}
            onUploadContainer={handleOpenBulkUpload}
          />
        </Suspense>

        {/* ===== CREATE CONTAINER DIALOG ===== */}
        <Dialog
          open={createContainerDialogOpen}
          onClose={handleCloseCreateContainer}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle sx={{
            bgcolor: 'primary.main',
            color: 'white',
            px: 3,
            py: 2
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Box display="flex" alignItems="center">
                <DirectionsBoatIcon sx={{ mr: 2 }} />
                Create New Container
              </Box>
              <IconButton
                onClick={handleCloseCreateContainer}
                size="small"
                sx={{ color: 'white' }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent dividers>
            {resourcesLoading && <LinearProgress sx={{ mb: 2 }} />}

            {formError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {formError}
              </Alert>
            )}

            <Box sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
              gap: 2,
              mt: 1
            }}>
              {/* Container Number */}
              <TextField
                ref={firstInputRef}
                label="Container Number"
                value={formData.containerNo}
                onChange={(e) => handleFormChange('containerNo', e.target.value)}
                fullWidth
                required
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <DirectionsBoatIcon fontSize="small" />
                    </InputAdornment>
                  )
                }}
              />

              {/* Client Selection */}
              <Autocomplete
                options={clients}
                getOptionLabel={(option) => option.companyName || option.username}
                value={clients.find(c => c.username === formData.clientUsername) || null}
                onChange={(_, value) => handleClientChange(value?.username || '')}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Client"
                    required
                    size="small"
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon fontSize="small" />
                        </InputAdornment>
                      )
                    }}
                  />
                )}
                loading={resourcesLoading}
              />

              {/* Truck Number */}
              <TextField
                label="Truck Number"
                value={formData.truckNo}
                onChange={(e) => handleFormChange('truckNo', e.target.value)}
                fullWidth
                required
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LocalShippingIcon fontSize="small" />
                    </InputAdornment>
                  )
                }}
              />

              {/* Vessel/Voyage Number */}
              <TextField
                label="Vessel/Voyage Number"
                value={formData.vesselVoyageNo}
                onChange={(e) => handleFormChange('vesselVoyageNo', e.target.value)}
                fullWidth
                required
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <DirectionsBoatIcon fontSize="small" />
                    </InputAdornment>
                  )
                }}
              />

              {/* Manifest Quantity */}
              <TextField
                label="Manifest Quantity"
                type="number"
                value={formData.manifestQuantity}
                onChange={(e) => handleFormChange('manifestQuantity', parseInt(e.target.value) || 0)}
                fullWidth
                required
                size="small"
                inputProps={{ min: 0 }}
              />

              {/* Loading Bay */}
              <TextField
                label="Loading Bay"
                value={formData.loadingBay}
                onChange={(e) => handleFormChange('loadingBay', e.target.value)}
                fullWidth
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <WarehouseIcon fontSize="small" />
                    </InputAdornment>
                  )
                }}
              />

              {/* ETA Requested Date */}
              <Box position="relative" width="100%">
                <CustomDatePickerWithCounts
                  label="ETA Requested Date"
                  value={formData.etaRequestedDate}
                  onChange={(newValue) => handleFormChange('etaRequestedDate', newValue)}
                  containers={[]}
                  slotProps={{
                    popper: {
                      sx: {
                        zIndex: 10001,
                      }
                    }
                  }}
                />
              </Box>

              {/* Unstuff Team */}
              <TextField
                label="Unstuff Team"
                value={formData.unstuffTeam}
                onChange={(e) => handleFormChange('unstuffTeam', e.target.value)}
                fullWidth
                size="small"
              />

              {/* Remarks */}
              <TextField
                label="Remarks"
                value={formData.remark}
                onChange={(e) => handleFormChange('remark', e.target.value)}
                fullWidth
                multiline
                rows={2}
                size="small"
                sx={{ gridColumn: 'span 2' }}
              />
            </Box>
          </DialogContent>

          <DialogActions sx={{ px: 3, py: 2 }}>
            <Button onClick={handleCloseCreateContainer}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateContainer}
              variant="contained"
              disabled={formLoading || !formData.containerNo || !formData.clientUsername || !formData.truckNo}
              startIcon={formLoading ? <CircularProgress size={16} /> : <AddIcon />}
            >
              {formLoading ? 'Creating...' : 'Create Container'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* ===== BULK UPLOAD DIALOG ===== */}
        <Dialog
          open={bulkUploadDialogOpen}
          onClose={() => setBulkUploadDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle sx={{
            bgcolor: 'primary.main',
            color: 'white',
            px: 3,
            py: 2
          }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Box display="flex" alignItems="center">
                <CloudUploadIcon sx={{ mr: 2 }} />
                Container Bulk Upload
              </Box>
              <IconButton
                onClick={() => setBulkUploadDialogOpen(false)}
                size="small"
                sx={{ color: 'white' }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent dividers>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Upload multiple containers from an Excel file. Please ensure your file follows the required format.
            </Typography>

            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Excel Template Required
              </Typography>
              <Typography variant="body2">
                Download our Excel template with the correct column headers and format requirements.
              </Typography>
            </Alert>

            {/* File upload area would go here */}
            <Box sx={{
              border: '2px dashed',
              borderColor: 'divider',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              bgcolor: 'background.default'
            }}>
              <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Drag and drop your Excel file here
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                or click to browse files
              </Typography>
              <Button variant="outlined" component="label">
                Choose File
                <input type="file" hidden accept=".xlsx,.xls" />
              </Button>
            </Box>
          </DialogContent>

          <DialogActions sx={{ px: 3, py: 2 }}>
            <Button onClick={() => setBulkUploadDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="outlined" startIcon={<GetAppIcon />}>
              Download Template
            </Button>
            <Button variant="contained" startIcon={<UploadFileIcon />} disabled>
              Upload Containers
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Box>
  );
};

export default ContainerManagementTest;
