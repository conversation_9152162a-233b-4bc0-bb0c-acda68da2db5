import React, { useRef, useState, useEffect, useCallback } from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  IconButton,
  Box,
  Paper,
  Typography,
  Divider,
  Tooltip,
  Snackbar,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PrintIcon from '@mui/icons-material/Print';
import { useReactToPrint } from 'react-to-print';
import QRCode from 'react-qr-code';
import Barcode from 'react-barcode';
import { Pallet } from '../types/Pallet';
import { Manifest } from '../types/manifest';
import { useToast } from '../contexts/ToastContext';
import { formatTimeSlot, formatDateOnly } from '../utils/dateUtils';
import { useAuth } from '../contexts/AuthContext';
import locationService from '../services/location.service';
import { getVehicleTypeAbbreviation } from '../services/vehicle.service';

// Add print-specific styles
const printStyles = `
  @media print {
    @page {
      size: auto;
      margin: 10mm;
    }
    body {
      margin: 0;
      padding: 0;
    }
  }
`;

// Ultra aggressive font sizing to ensure all text fits in single line within fixed container
const getFontSizeForText = (text: string): string => {
  if (!text) return '80px';
  const length = text.length;

  // Ultra aggressive scaling - make text very small to fit any length
  if (length > 60) return '10px';   // Insanely long text
  if (length > 55) return '11px';   // Extremely insanely long text
  if (length > 50) return '12px';   // Super insanely long text
  if (length > 45) return '13px';   // Very insanely long text
  if (length > 40) return '14px';   // Ultra extremely long text
  if (length > 35) return '15px';   // Super extremely long text
  if (length > 32) return '16px';   // Very extremely long text
  if (length > 30) return '17px';   // Extremely long text
  if (length > 28) return '18px';   // Very very long text
  if (length > 25) return '20px';   // Very long text
  if (length > 22) return '22px';   // Extra long text
  if (length > 20) return '24px';   // Long text
  if (length > 18) return '26px';   // Medium-long text
  if (length > 16) return '28px';   // Medium-long text
  if (length > 15) return '32px';   // Medium text
  if (length > 13) return '36px';   // Medium text
  if (length > 12) return '40px';   // Medium-short text
  if (length > 10) return '46px';   // Short-medium text
  if (length > 8) return '52px';    // Short text
  if (length > 7) return '58px';    // Reference point
  if (length > 6) return '66px';    // Very short text
  if (length > 5) return '74px';    // Extra short text
  if (length > 4) return '82px';    // Super short text
  if (length > 3) return '90px';    // Ultra short text
  if (length > 2) return '98px';    // Minimal text
  return '106px';  // Single/double character text
};

interface PalletLabelDialogProps {
  open: boolean;
  onClose: () => void;
  pallets: Pallet[];
  manifest: Manifest | null;
  allManifestPallets?: Pallet[]; // Add all pallets for proper cumulative calculation
}

const PalletLabelDialog: React.FC<PalletLabelDialogProps> = ({ 
  open, 
  onClose, 
  pallets,
  manifest,
  allManifestPallets
}) => {
  const printRef = useRef<HTMLDivElement>(null);
  const printButtonRef = useRef<HTMLButtonElement>(null);
  const toast = useToast();
  const { currentUser } = useAuth();
  const [locationAbbr, setLocationAbbr] = useState<string>('N');
  const [vehicleAbbr, setVehicleAbbr] = useState<string>('V');
  const [customToast, setCustomToast] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  const [isPrinting, setIsPrinting] = useState(false);
  const [printStarted, setPrintStarted] = useState(false);
  const printTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to format date with time slot instead of time
  const formatDateWithTimeSlot = (deliveryDate?: string | null, timeSlot?: string | null): string => {
    if (!deliveryDate) return '_____________________';

    // Get just the date part (without time)
    const dateOnly = formatDateOnly(new Date(deliveryDate));

    // For 'no_time' slot, show date with empty line for time slot
    if (timeSlot === 'no_time') {
      return `${dateOnly} ______________`;
    }

    // Get the time slot text for other time slots
    const timeSlotText = timeSlot ? formatTimeSlot(timeSlot) : '';

    // Combine date with time slot (with space only if time slot exists)
    return timeSlotText ? `${dateOnly} ${timeSlotText}` : dateOnly;
  };

  // Reset toast state when dialog opens
  useEffect(() => {
    if (open) {
      setCustomToast({
        open: false,
        message: '',
        severity: 'success' as 'success' | 'error' | 'info' | 'warning'
      });
    }
  }, [open]);

  // Debug log to see if component is rendering and what data it has
  React.useEffect(() => {
    if (open) {
      console.log('🎯 PalletLabelDialog opened with data:', {
        pallets: pallets?.length || 0,
        manifest: manifest ? {
          trackingNo: manifest.trackingNo,
          location: manifest.location,
          deliveryVehicle: manifest.deliveryVehicle
        } : null
      });
    }
  }, [open, pallets, manifest]);

  // Fetch dynamic abbreviations when dialog opens
  useEffect(() => {
    if (open && manifest && currentUser?.token) {
      fetchAbbreviations();
    }
  }, [open, manifest, currentUser?.token]);

  // Focus the print button when dialog opens
  useEffect(() => {
    if (open && printButtonRef.current) {
      setTimeout(() => {
        printButtonRef.current?.focus();
      }, 200);
    }
  }, [open]);

  const fetchAbbreviations = async () => {
    try {
      // Fetch location abbreviation
      if (manifest?.location) {
        try {
          const locationResponse = await locationService.getLocationAbbreviation(manifest.location, currentUser!.token);
          if (locationResponse.success && locationResponse.data) {
            setLocationAbbr(locationResponse.data);
            console.log('Using location abbreviation from API:', locationResponse.data);
          } else {
            console.warn('Location abbreviation API returned no data or failed, using default "N"');
            setLocationAbbr('N');
          }
        } catch (error) {
          console.error('Failed to fetch location abbreviation from API:', error);
          setLocationAbbr('N');
        }
      } else {
        setLocationAbbr('N');
      }

      // Fetch vehicle type abbreviation
      if (manifest?.deliveryVehicle) {
        try {
          const vehicleAbbreviation = await getVehicleTypeAbbreviation(manifest.deliveryVehicle);
          console.log('Using vehicle abbreviation from API:', vehicleAbbreviation);
          setVehicleAbbr(vehicleAbbreviation);
        } catch (error) {
          console.error('Failed to fetch vehicle abbreviation from API:', error);
          setVehicleAbbr('V');
        }
      } else {
        setVehicleAbbr('V');
      }
    } catch (error) {
      console.error('Error fetching abbreviations:', error);
      setLocationAbbr('N');
      setVehicleAbbr('V');
    }
  };

  // Function to show toast message globally (outside the dialog)
  const showGlobalToast = useCallback((message: string, severity: 'success' | 'error') => {
    // Use parent document body to attach the toast
    const toastRoot = document.createElement('div');
    toastRoot.id = 'print-toast-container';
    document.body.appendChild(toastRoot);
    
    // Create and render the toast
    const toastElement = document.createElement('div');
    toastElement.className = 'print-toast';
    toastElement.style.position = 'fixed';
    toastElement.style.top = '5%';
    toastElement.style.left = '50%';
    toastElement.style.transform = 'translateX(-50%)';
    toastElement.style.backgroundColor = severity === 'success' ? '#4caf50' : '#f44336';
    toastElement.style.color = 'white';
    toastElement.style.padding = '12px 24px';
    toastElement.style.borderRadius = '4px';
    toastElement.style.boxShadow = '0 3px 5px rgba(0,0,0,0.2)';
    toastElement.style.zIndex = '10000';
    toastElement.style.fontWeight = 'bold';
    toastElement.style.fontSize = '1.1rem';
    toastElement.textContent = message;
    
    toastRoot.appendChild(toastElement);
    
    // Remove after timeout - increased to 6 seconds
    setTimeout(() => {
      if (document.body.contains(toastRoot)) {
        document.body.removeChild(toastRoot);
      }
    }, 6000);
  }, []);

  // Setup react-to-print
  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: `Pallet_Labels_${manifest?.trackingNo || 'Batch'}`,
    onBeforePrint: async () => {
      setIsPrinting(true);
      setPrintStarted(true);
      
      // Set a timeout to reset the print started flag if print is canceled
      // This helps detect if the user closed the print dialog without printing
      if (printTimeoutRef.current) {
        clearTimeout(printTimeoutRef.current);
      }
      
      return Promise.resolve();
    },
    onAfterPrint: () => {
      console.log(`Pallet labels print completed - ${pallets.length} labels`);
      setIsPrinting(false);
      
      // Only show success message if print was actually started
      if (printStarted) {
        // Clear any pending timeout
        if (printTimeoutRef.current) {
          clearTimeout(printTimeoutRef.current);
        }
        
        // Close the dialog immediately
        onClose();
        
        // Create a more detailed success message with pallet numbers
        let successMessage = '';
        if (pallets.length === 1) {
          successMessage = `Successfully printed label for Pallet No. ${pallets[0].palletNo}`;
        } else {
          const palletNumbers = pallets.map(p => p.palletNo).join(', ');
          successMessage = `Successfully printed ${pallets.length} labels for Pallet Nos. ${palletNumbers}`;
        }
        
        // Show global toast message that's not tied to the dialog
        showGlobalToast(successMessage, 'success');
        
        // Reset print started flag
        setPrintStarted(false);
      }
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
      setIsPrinting(false);
      setPrintStarted(false);
      
      // Clear any pending timeout
      if (printTimeoutRef.current) {
        clearTimeout(printTimeoutRef.current);
      }
      
      // Close the dialog immediately
      onClose();
      
      // Show global error toast
      showGlobalToast(`Print error: ${error ? String(error) : 'Unknown error'}`, 'error');
    },
    pageStyle: printStyles
  });

  const handleCloseToast = () => {
    setCustomToast({...customToast, open: false});
  };

  const triggerPrint = () => {
    console.log(`Attempting to print ${pallets.length} pallet labels. printRef.current:`, printRef.current);
    
    // Reset any existing toast before starting a new print
    setCustomToast({
      open: false,
      message: '',
      severity: 'success' as 'success' | 'error' | 'info' | 'warning'
    });
    
    if (!printRef.current) {
      console.error("Cannot print, printRef.current is null or undefined.");
      setCustomToast({
        open: true,
        message: "Error: Printable content not found. Please try again.",
        severity: 'error'
      });
      return;
    }
    
    try {
      // Set a timeout to detect if print was canceled by user closing the dialog
      if (printTimeoutRef.current) {
        clearTimeout(printTimeoutRef.current);
      }
      
      printTimeoutRef.current = setTimeout(() => {
        // If this timeout executes, it means onAfterPrint wasn't called quickly
        // which likely means the user closed the print dialog without printing
        setPrintStarted(false);
      }, 500);
      
      // Don't show any message here, just trigger the print
      handlePrint();
    } catch (error) {
      console.error("Error during print:", error);
      setPrintStarted(false);
      setCustomToast({
        open: true,
        message: `Printing failed: ${error}`,
        severity: 'error'
      });
    }
  };

  // Handle keyboard events for the dialog
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Print when Enter or P is pressed
    if (e.key === 'Enter' || e.key === 'p' || e.key === 'P') {
      e.preventDefault();
      triggerPrint();
    }
    // Close dialog when Escape is pressed
    else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  if (!manifest || pallets.length === 0) return null;

  return (
    <>
      <Dialog 
        open={open} 
        onClose={onClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: 24,
          },
        }}
        onKeyDown={handleKeyDown}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h6" component="div">
                Pallet Labels - {manifest?.trackingNo}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {pallets.length} pallet{pallets.length !== 1 ? 's' : ''} • Professional manifest-style labels with barcode & QR
              </Typography>
            </Box>
            <Box display="flex" alignItems="center">
              <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                Press Enter to print or Esc to close
              </Typography>
              <IconButton
                aria-label="close"
                onClick={onClose}
                size="small"
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
        </DialogTitle>
        
        <DialogContent dividers>
          {/* Content to print */}
          <div 
            ref={printRef} 
            style={{ width: '100%' }} 
            className="printable-content"
            data-testid="pallet-print-content"
          >
            {pallets.map((pallet, index) => {
              // Use all manifest pallets if available, otherwise fall back to current pallets
              const allPallets = allManifestPallets || pallets;
              
              // Sort pallets by creation date or pallet number to ensure correct order
              const sortedPallets = [...allPallets].sort((a, b) => {
                // Try to sort by pallet number if it contains sequence info
                const aNum = a.palletNo.match(/(\d+)$/)?.[1];
                const bNum = b.palletNo.match(/(\d+)$/)?.[1];
                if (aNum && bNum) {
                  return parseInt(aNum) - parseInt(bNum);
                }
                // Fallback to string comparison
                return a.palletNo.localeCompare(b.palletNo);
              });
              
              // Find the current pallet's position in the sorted list
              const currentPalletIndex = sortedPallets.findIndex(p => p.palletNo === pallet.palletNo);
              
              // Calculate cumulative pieces up to and including this pallet
              const cumulativePieces = sortedPallets
                .slice(0, currentPalletIndex + 1)
                .reduce((sum, p) => sum + p.noOfPieces, 0);
              
              return (
              <Box 
                key={pallet.palletNo} 
                sx={{ 
                  pageBreakAfter: index < pallets.length - 1 ? 'always' : 'auto', 
                  mb: index < pallets.length - 1 ? 4 : 0 
                }}
              >
                <Paper 
                  sx={{ 
                    p: 0.5,
                    width: '560px',
                    height: '370px',
                    margin: '0 auto',
                    border: '1px solid #000',
                    display: 'flex',
                    flexDirection: 'row'
                  }}
                >
                  {/* Right Column - All other content */}
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    flex: 1,
                    justifyContent: 'center'
                  }}>
                    {/* Tracking & Internal ID - Fixed Size Container */}
                    <Box sx={{
                      height: '120px', // Fixed height for consistent container size
                      minHeight: '120px', // Ensure minimum height
                      maxHeight: '120px', // Prevent expansion
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      border: '2px solid #000',
                      justifyContent: 'center',
                      bgcolor: '#fff',
                      overflow: 'hidden', // Prevent content overflow
                      padding: '8px' // Add padding for better text spacing
                    }}>
                      {(() => {
                        const internalIdText = manifest?.internalId || `${manifest?.container?.containerNo}-${manifest?.sequenceNo}`;
                        const fontSize = getFontSizeForText(internalIdText);
                        return (
                          <Typography
                            variant="h1"
                            fontWeight="900"
                            sx={{
                              fontSize,
                              width: '100%',
                              textAlign: 'center',
                              lineHeight: 1, // Single line height
                              whiteSpace: 'nowrap', // Prevent text wrapping completely
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: '100%', // Take full height of container
                              maxWidth: '100%' // Ensure it doesn't exceed container width
                            }}
                          >
                            {internalIdText}
                          </Typography>
                        );
                      })()}
                    </Box>
                    {/* Date Section with Time Slot */}
                    <Box sx={{ mt: 1, mb: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', flex: '0 0 auto' }}>
                      <Typography variant="h4" fontWeight="900" sx={{ fontSize: '24px' }}>
                        Date: {formatDateWithTimeSlot(manifest?.deliveryDate, manifest?.timeSlot)}
                      </Typography>
                    </Box>

                    {/* Large Barcode Section */}
                    <Box sx={{ display: 'flex', justifyContent: 'center', flex: '0 0 auto', mb: 1}}>
                      <Barcode 
                        value={manifest?.trackingNo || ''} 
                        width={1.5}
                        height={50}
                        fontSize={25}
                        fontOptions="bold"
                        margin={0}
                        displayValue={true}
                      />
                    </Box>

                    {/* Main content - Pallet Details with QR Code */}
                    <Box sx={{ display: 'flex', mb: 0.5, flex: '0 0 auto', minHeight: 0, alignItems: 'center' }}>
                      {/* Pallet Details */}
                      <Box sx={{ flex: 1, pr: 0 }}>
                        {(() => {
                          const internalIdText = manifest?.internalId || `${manifest?.container?.containerNo}-${manifest?.sequenceNo}`;
                          const internalIdFontSize = parseInt(getFontSizeForText(internalIdText));

                          // Scale pallet details to fit container - use proportional sizing
                          // For normal internal ID (58px), use 25px for pallet details
                          // Scale proportionally but with reasonable min/max bounds
                          const scaleFactor = internalIdFontSize / 58; // 58px is our reference size
                          const basePalletFontSize = 25;
                          const scaledPalletFontSize = Math.max(12, Math.min(30, basePalletFontSize * scaleFactor));
                          const scaledLabelFontSize = Math.max(8, scaledPalletFontSize * 0.6);

                          return (
                            <>
                              <Typography variant="h4" fontWeight="900" sx={{ fontSize: `${scaledPalletFontSize}px`, display: 'flex', alignItems: 'center' }}>
                                <Typography component="span" fontWeight="900" sx={{ fontSize: `${scaledLabelFontSize}px` }}>PalletNo.:</Typography>&nbsp;{pallet.palletNo}
                              </Typography>
                              <Typography variant="h4" fontWeight="900" sx={{ fontSize: `${scaledPalletFontSize}px`, display: 'flex', alignItems: 'center' }}>
                                <Typography component="span" fontWeight="900" sx={{ fontSize: `${scaledLabelFontSize}px` }}>Pieces on pallet:</Typography>&nbsp;{pallet.noOfPieces}
                              </Typography>
                              <Typography variant="h4" fontWeight="900" sx={{ fontSize: `${scaledPalletFontSize}px`, display: 'flex', alignItems: 'center' }}>
                                <Typography component="span" fontWeight="900" sx={{ fontSize: `${scaledLabelFontSize}px` }}>Inb Pieces/Pieces:</Typography>&nbsp;{cumulativePieces}/{pallet.totalPiecesReference}
                              </Typography>
                            </>
                          );
                        })()}
                      </Box>

                      {/* QR Code */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100px', mr: 0.5,}}>
                        <QRCode
                          value={manifest?.trackingNo || ''}
                          size={100}
                          level="M"
                        />
                      </Box>
                    </Box>
                  </Box>

                  {/* Left Column - Location and Vehicle */}
                  <Box sx={{ display: 'flex', flexDirection: 'column', width: '170px', gap: 1, ml: 0.5 }}>
                    {/* Location Box - Top Left */}
                    <Box sx={{ 
                      flex: 1,
                      border: '2px solid #000',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: '#fff'
                    }}>
                      <Typography variant="caption" sx={{ fontSize: '20px', fontWeight: '900', lineHeight: 1, mb: 0 }}>
                        LOCATION
                      </Typography>
                      <Typography variant="h1" fontWeight="bold" sx={{ fontSize: '125px', lineHeight: 0.9, mt: 0 }}>
                        {locationAbbr}
                      </Typography>
                    </Box>
                    
                    {/* Vehicle Box - Bottom Left */}
                    <Box sx={{ 
                      flex: 1,
                      border: '2px solid #000',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: '#fff'
                    }}>
                      <Typography variant="caption" sx={{ fontSize: '20px', fontWeight: '900', lineHeight: 1, mb: 0 }}>
                        VEHICLE
                      </Typography>
                      <Typography variant="h1" fontWeight="bold" sx={{ fontSize: '125px', lineHeight: 0.9, mt: 0 }}>
                        {vehicleAbbr}
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              </Box>
            );
          })}
          </div>
        </DialogContent>
        
        <DialogActions>
          <Button 
            variant="contained" 
            startIcon={<PrintIcon />} 
            onClick={triggerPrint}
            ref={printButtonRef}
            autoFocus
          >
            Print {pallets.length} Pallet Label{pallets.length !== 1 ? 's' : ''}
          </Button>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Custom toast notification that appears above the dialog */}
      <Snackbar
        open={customToast.open}
        autoHideDuration={3000}
        onClose={handleCloseToast}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ 
          zIndex: 10000,
          position: 'fixed',
          top: '5%',
          left: '50%',
          transform: 'translateX(-50%)',
          '& .MuiPaper-root': {
            fontSize: '1.1rem',
            padding: '12px 24px'
          }
        }}
      >
        <Alert
          onClose={handleCloseToast}
          severity={customToast.severity}
          sx={{ 
            width: '100%', 
            boxShadow: 3, 
            fontWeight: 'bold',
            backgroundColor: theme => customToast.severity === 'success' ? theme.palette.success.main : theme.palette.error.main,
            color: 'white'
          }}
        >
          {customToast.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default PalletLabelDialog; 