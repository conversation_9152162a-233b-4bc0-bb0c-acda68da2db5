import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Box,
  CssBaseline,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Button,
  Avatar,
  useTheme,
  Tooltip,
  Zoom,
  Badge,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import HomeIcon from '@mui/icons-material/Home';
import PeopleIcon from '@mui/icons-material/People';
import InventoryIcon from '@mui/icons-material/Inventory';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import AccountBoxIcon from '@mui/icons-material/AccountBox';
import LogoutIcon from '@mui/icons-material/Logout';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import DashboardIcon from '@mui/icons-material/Dashboard';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import CategoryIcon from '@mui/icons-material/Category';
import { useAuth } from '../contexts/AuthContext';
import { useThemeContext } from '../contexts/ThemeContext';

const expandedDrawerWidth = 250;
const collapsedDrawerWidth = 68;

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  // Check localStorage for saved state, default to expanded
  const getSavedSidebarState = (): boolean => {
    try {
      const saved = localStorage.getItem('sidebarCollapsed');
      return saved === 'true';
    } catch (e) {
      return false;
    }
  };

  const [mobileOpen, setMobileOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(getSavedSidebarState());
  const { currentUser, logout, hasRole } = useAuth();
  const { mode } = useThemeContext();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();

  const drawerWidth = isCollapsed ? collapsedDrawerWidth : expandedDrawerWidth;

  // Save sidebar state to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
    } catch (e) {
      console.error('Failed to save sidebar state to localStorage', e);
    }
  }, [isCollapsed]);

  // Close mobile drawer when location changes
  useEffect(() => {
    setMobileOpen(false);
  }, [location]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const adminMenuItems = [
    { text: 'Dashboard', icon: <HomeIcon />, path: '/dashboard' },
    { text: 'Modern Dashboard', icon: <DashboardIcon />, path: '/modern-dashboard' },
    { text: 'User Management', icon: <PeopleIcon />, path: '/users' },
    { text: 'Container Management', icon: <InventoryIcon />, path: '/containers' },
    { text: 'Manifest Management', icon: <LocalShippingIcon />, path: '/manifests' },
    { text: 'Vehicle Types', icon: <CategoryIcon />, path: '/vehicle-types' },
    { text: 'Location Zones', icon: <LocationOnIcon />, path: '/location-zones' },
    { text: 'Profile', icon: <AccountBoxIcon />, path: '/profile' },
  ];

  const managerMenuItems = [
    { text: 'Dashboard', icon: <HomeIcon />, path: '/dashboard' },
    { text: 'Modern Dashboard', icon: <DashboardIcon />, path: '/modern-dashboard' },
    { text: 'Container Management', icon: <InventoryIcon />, path: '/containers' },
    { text: 'Manifest Management', icon: <LocalShippingIcon />, path: '/manifests' },
    { text: 'Vehicle Types', icon: <CategoryIcon />, path: '/vehicle-types' },
    { text: 'Profile', icon: <AccountBoxIcon />, path: '/profile' },
  ];

  const clientMenuItems = [
    { text: 'Dashboard', icon: <HomeIcon />, path: '/dashboard' },
    { text: 'Modern Dashboard', icon: <DashboardIcon />, path: '/modern-dashboard' },
    { text: 'My Containers', icon: <InventoryIcon />, path: '/my-containers' },
    { text: 'Profile', icon: <AccountBoxIcon />, path: '/profile' },
  ];

  const driverMenuItems = [
    { text: 'Dashboard', icon: <HomeIcon />, path: '/dashboard' },
    { text: 'Modern Dashboard', icon: <DashboardIcon />, path: '/modern-dashboard' },
    { text: 'My Manifests', icon: <LocalShippingIcon />, path: '/my-manifests' },
    { text: 'Profile', icon: <AccountBoxIcon />, path: '/profile' },
  ];

  const getMenuItems = () => {
    if (hasRole('ROLE_ADMIN')) return adminMenuItems;
    if (hasRole('ROLE_MANAGER')) return managerMenuItems;
    if (hasRole('ROLE_CLIENT')) return clientMenuItems;
    if (hasRole('ROLE_DRIVER')) return driverMenuItems;
    return [];
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const drawer = (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column', 
      bgcolor: '#f5f5f5',
      position: 'relative',
      borderRight: '1px solid rgba(0, 0, 0, 0.08)',
      zIndex: 1300,
      touchAction: 'auto',
      pointerEvents: 'auto',
      '& *': {
        pointerEvents: 'auto'
      },
      // Improve focus management
      '& .MuiButtonBase-root': {
        '&:focus-visible': {
          outline: `2px solid ${theme.palette.primary.main}`,
          outlineOffset: '2px',
        },
        '&:focus:not(:focus-visible)': {
          outline: 'none',
        }
      }
    }}>
      {/* Header */}
      <Box sx={{ 
        p: isCollapsed ? 1 : 2, 
        height: 64,
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        bgcolor: theme.palette.primary.main,
        color: 'white',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        transition: theme.transitions.create(['padding'], {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.shorter,
        }),
      }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center',
          overflow: 'hidden',
          flexGrow: 1,
          justifyContent: isCollapsed ? 'center' : 'flex-start',
          transition: theme.transitions.create('justify-content', {
            duration: theme.transitions.duration.standard,
          }),
        }}>
          <Avatar
            sx={{ 
              bgcolor: isCollapsed ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
              p: isCollapsed ? 0.75 : 0,
              mr: isCollapsed ? 0 : 1.5, 
              width: 36,
              height: 36,
              transition: theme.transitions.create(['background-color', 'padding', 'margin'], {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.standard,
              }),
            }} 
          >
            <WarehouseIcon 
              sx={{ 
                fontSize: 22,
                color: 'white',
                transition: theme.transitions.create(['transform'], {
                  easing: theme.transitions.easing.easeInOut,
                  duration: theme.transitions.duration.standard,
                }),
                transform: isCollapsed ? 'scale(1.1)' : 'scale(1)',
              }} 
            />
          </Avatar>
          {!isCollapsed && (
            <Typography 
              variant="h6" 
              noWrap 
              component="div"
              sx={{
                fontWeight: 600,
                letterSpacing: '0.5px',
                opacity: isCollapsed ? 0 : 1,
                transition: theme.transitions.create(['opacity', 'transform'], {
                  easing: theme.transitions.easing.easeInOut,
                  duration: theme.transitions.duration.shorter,
                }),
                transform: isCollapsed ? 'translateX(-20px)' : 'translateX(0)',
              }}
            >
          Fukuyama WMS
        </Typography>
          )}
        </Box>
        
        {/* Collapse toggle button - now in the header */}
        <Tooltip 
          title={isCollapsed ? "Expand menu" : "Collapse menu"} 
          placement={isCollapsed ? "right" : "bottom"}
          TransitionComponent={Zoom}
          arrow
        >
          <IconButton 
            onClick={handleToggleCollapse}
            size="small"
            sx={{ 
              p: 0.5,
              ml: isCollapsed ? 0 : 1,
              color: 'white',
              bgcolor: 'rgba(255, 255, 255, 0.15)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: 0, // Remove rounded corners
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.25)',
                transform: 'scale(1.05)',
              },
              transition: theme.transitions.create(['background-color', 'transform', 'margin'], {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.shortest,
              }),
            }}
          >
            {isCollapsed ? 
              <ChevronRightIcon fontSize="small" /> : 
              <ChevronLeftIcon fontSize="small" />
            }
          </IconButton>
        </Tooltip>
      </Box>

      {/* Navigation menu */}
      <List sx={{ 
        flexGrow: 1, 
        pt: 1.5,
        pb: 1,
        px: isCollapsed ? 0.75 : 1,
        overflowY: 'auto',
        overflowX: 'hidden',
        '&::-webkit-scrollbar': {
          width: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: 'rgba(0,0,0,0.2)',
          borderRadius: '4px',
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: 'rgba(0,0,0,0.05)',
        },
        transition: theme.transitions.create('padding', {
          easing: theme.transitions.easing.easeInOut,
          duration: theme.transitions.duration.standard,
        }),
      }}>
        {getMenuItems().map((item) => {
          const isItemActive = isActive(item.path);
          
          const listItem = (
            <ListItemButton 
              component={Link} 
              to={item.path} 
              selected={isItemActive}
              sx={{
                minHeight: 48,
                mb: 0.5,
                px: isCollapsed ? 1.5 : 2,
                py: 1,
                justifyContent: isCollapsed ? 'center' : 'flex-start',
                borderRadius: 0,
                position: 'relative',
                width: '100%',
                '&.Mui-selected': {
                  bgcolor: isCollapsed ? 'rgba(25, 118, 210, 0.15)' : 'rgba(25, 118, 210, 0.12)',
                  '&:hover': {
                    bgcolor: 'rgba(25, 118, 210, 0.18)',
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: isCollapsed ? '15%' : '20%',
                    height: isCollapsed ? '70%' : '60%',
                    width: '4px',
                    bgcolor: theme.palette.primary.main,
                    borderRadius: '0 4px 4px 0',
                  }
                },
                '&:hover': {
                  bgcolor: isCollapsed ? 'rgba(0, 0, 0, 0.06)' : 'rgba(0, 0, 0, 0.04)',
                  '& .MuiListItemIcon-root': {
                    transform: 'scale(1.15)',
                    color: isItemActive ? theme.palette.primary.main : theme.palette.primary.light,
                  }
                },
                // Improve focus management
                '&:focus': {
                  outline: 'none',
                  bgcolor: 'rgba(25, 118, 210, 0.08)',
                },
                '&:focus-visible': {
                  outline: `2px solid ${theme.palette.primary.main}`,
                  outlineOffset: '-2px',
                },
                transition: theme.transitions.create(['background-color', 'padding'], {
                  easing: theme.transitions.easing.easeInOut,
                  duration: theme.transitions.duration.shorter,
                }),
              }}
            >
              <ListItemIcon 
                sx={{ 
                  color: isItemActive ? theme.palette.primary.main : 'rgba(0, 0, 0, 0.6)',
                  minWidth: isCollapsed ? 0 : 40,
                  mr: isCollapsed ? 0 : 2.5,
                  justifyContent: 'center',
                  transition: theme.transitions.create(['transform', 'margin', 'color'], {
                    easing: theme.transitions.easing.easeInOut,
                    duration: theme.transitions.duration.shortest,
                  }),
                  ...(isCollapsed && isItemActive && {
                    color: theme.palette.primary.main,
                    transform: 'scale(1.2)',
                  }),
                }}
              >
                {isCollapsed && isItemActive ? (
                  <Badge
                    variant="dot"
                    color="primary"
                    overlap="circular"
                    sx={{
                      '& .MuiBadge-badge': {
                        right: 3,
                        top: 3,
                        border: `2px solid ${theme.palette.background.paper}`,
                        padding: '0 4px',
                      },
                    }}
                  >
                {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </ListItemIcon>
              
              {!isCollapsed && (
              <ListItemText 
                primary={item.text} 
                primaryTypographyProps={{
                    fontWeight: isItemActive ? 600 : 500,
                    fontSize: '0.95rem',
                    display: 'block',
                    overflow: 'visible',
                    textOverflow: 'clip',
                    whiteSpace: 'normal',
                    lineHeight: '1.2',
                  }} 
                  sx={{
                    opacity: isCollapsed ? 0 : 1,
                    m: 0,
                    flex: '1 1 auto',
                    maxWidth: '100%',
                    transition: theme.transitions.create(['opacity', 'font-weight', 'color'], {
                      easing: theme.transitions.easing.easeInOut,
                      duration: theme.transitions.duration.shorter,
                    }),
                  }}
                />
              )}
            </ListItemButton>
          );

          return (
            <ListItem key={item.text} disablePadding sx={{ 
              display: 'block',
              width: '100%', 
              overflow: 'visible'
            }}>
              {isCollapsed ? (
                <Tooltip 
                  title={
                    <Box sx={{ 
                      py: 0.5, 
                      px: 0.5, 
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{ 
                        display: 'inline-flex', 
                        color: theme.palette.primary.main,
                        bgcolor: 'rgba(25, 118, 210, 0.1)',
                        p: 0.5,
                        borderRadius: '50%'
                      }}>
                        {item.icon}
                      </Box>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: isItemActive ? 600 : 500,
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {item.text}
                      </Typography>
                    </Box>
                  }
                  placement="right"
                  arrow
                  TransitionComponent={Zoom}
                  enterDelay={200}
                  leaveDelay={0}
                  PopperProps={{
                    sx: {
                      "& .MuiTooltip-tooltip": {
                        bgcolor: "background.paper",
                        color: "text.primary",
                        boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                        border: "1px solid rgba(0,0,0,0.08)",
                        p: 0,
                        maxWidth: 'none',
                        borderRadius: 2
                      },
                      "& .MuiTooltip-arrow": {
                        color: "background.paper",
                        "&::before": {
                          border: "1px solid rgba(0,0,0,0.08)",
                          backgroundColor: "background.paper",
                        }
                      }
                    }
                  }}
                >
                  {listItem}
                </Tooltip>
              ) : (
                listItem
              )}
          </ListItem>
          );
        })}
      </List>
      
      <Divider sx={{ 
        mx: isCollapsed ? 0.75 : 2,
        transition: theme.transitions.create('margin', {
          easing: theme.transitions.easing.easeInOut,
          duration: theme.transitions.duration.shorter,
        }),
      }} />
      
      {/* Logout section */}
      <List sx={{ py: 0.5 }}>
        <ListItem disablePadding>
          {isCollapsed ? (
            <Tooltip 
              title={
                <Box sx={{ 
                  py: 0.5, 
                  px: 0.5, 
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <Box sx={{ 
                    display: 'inline-flex', 
                    color: theme.palette.error.main,
                    bgcolor: 'rgba(211, 47, 47, 0.1)',
                    p: 0.5,
                    borderRadius: '50%'
                  }}>
                    <LogoutIcon />
                  </Box>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      fontWeight: 500,
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Logout
                  </Typography>
                </Box>
              }
              placement="right"
              arrow
              TransitionComponent={Zoom}
              enterDelay={200}
              leaveDelay={0}
              PopperProps={{
                sx: {
                  "& .MuiTooltip-tooltip": {
                    bgcolor: "background.paper",
                    color: "text.primary",
                    boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                    border: "1px solid rgba(0,0,0,0.08)",
                    p: 0,
                    maxWidth: 'none',
                    borderRadius: 2
                  },
                  "& .MuiTooltip-arrow": {
                    color: "background.paper",
                    "&::before": {
                      border: "1px solid rgba(0,0,0,0.08)",
                      backgroundColor: "background.paper",
                    }
                  }
                }
              }}
            >
              <ListItemButton 
                onClick={handleLogout}
                sx={{
                  minHeight: 48,
                  mx: 0.75,
                  px: 1.5,
                  py: 1,
                  my: 0.5,
                  borderRadius: '10px',
                  justifyContent: 'center',
                  '&:hover': {
                    bgcolor: 'rgba(211, 47, 47, 0.08)',
                    '& .MuiListItemIcon-root': {
                      transform: 'scale(1.15)',
                      color: theme.palette.error.main,
                    }
                  }
                }}
              >
                <ListItemIcon sx={{ 
                  color: theme.palette.error.main,
                  minWidth: 0,
                  mr: 0,
                  justifyContent: 'center',
                  transition: theme.transitions.create('transform', {
                    easing: theme.transitions.easing.easeInOut,
                    duration: theme.transitions.duration.shortest,
                  }),
                }}>
                  <LogoutIcon />
                </ListItemIcon>
              </ListItemButton>
            </Tooltip>
          ) : (
          <ListItemButton 
            onClick={handleLogout}
            sx={{
                minHeight: 48,
                mx: 1,
                px: 2,
                py: 1,
                my: 0.5,
                borderRadius: 0,
              '&:hover': {
                bgcolor: 'rgba(211, 47, 47, 0.04)',
                  '& .MuiListItemIcon-root': {
                    transform: 'scale(1.1)',
                    color: theme.palette.error.main,
                  }
                },
              // Improve focus management
              '&:focus': {
                outline: 'none',
                bgcolor: 'rgba(211, 47, 47, 0.08)',
              },
              '&:focus-visible': {
                outline: `2px solid ${theme.palette.error.main}`,
                outlineOffset: '-2px',
              }
              }}
            >
              <ListItemIcon sx={{ 
                color: theme.palette.error.main,
                minWidth: 40,
                mr: 2.5,
                transition: theme.transitions.create('transform', {
                  easing: theme.transitions.easing.easeInOut,
                  duration: theme.transitions.duration.shortest,
                }),
              }}>
              <LogoutIcon />
            </ListItemIcon>
              <ListItemText 
                primary="Logout" 
                primaryTypographyProps={{
                  fontWeight: 500,
                  fontSize: '0.95rem'
                }}
              />
          </ListItemButton>
          )}
        </ListItem>
      </List>

      {/* Footer with version */}
      <Box sx={{ 
        py: 1, 
        px: 2, 
        display: 'flex', 
        justifyContent: isCollapsed ? 'center' : 'flex-start',
        borderTop: '1px solid rgba(0,0,0,0.05)',
        opacity: 0.7,
      }}>
        <Typography 
          variant="caption" 
          color="text.secondary"
          noWrap
          sx={{
            fontSize: '0.7rem',
            display: isCollapsed ? 'none' : 'block'
          }}
        >
          Fukuyama WMS v1.0.0
        </Typography>
        {isCollapsed && (
          <Typography 
            variant="caption" 
            color="text.secondary"
            noWrap
            sx={{
              fontSize: '0.7rem',
              fontWeight: 'bold'
            }}
          >
            1.0
          </Typography>
        )}
      </Box>
    </Box>
  );

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      minWidth: '100%',
      position: 'relative',
      boxSizing: 'border-box',
      margin: 0,
      padding: 0,
      overflow: 'hidden',
    }}>
      <CssBaseline />
      
      {/* Side Menu - Permanent Drawer (Desktop) */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': { 
            boxSizing: 'border-box', 
            width: drawerWidth,
            overflowX: 'visible',
            transition: theme.transitions.create(['width', 'transform'], {
              easing: theme.transitions.easing.easeOut,
              duration: theme.transitions.duration.standard,
            }),
            boxShadow: '0 0 10px rgba(0,0,0,0.05)',
            border: 'none',
            borderRadius: 0, // Remove rounded corners
            willChange: 'width',
          },
        }}
        open
      >
        {drawer}
      </Drawer>
      
      {/* Side Menu - Temporary Drawer (Mobile) */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
          hideBackdrop: true,
          disableEnforceFocus: false,
          disableAutoFocus: false,
          disableScrollLock: true,
          container: document.body,
          BackdropProps: {
            sx: {
              display: 'none',
            }
          },
          style: { zIndex: theme.zIndex.drawer }
        }}
        SlideProps={{
          style: { zIndex: theme.zIndex.drawer + 1 }
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': { 
            boxSizing: 'border-box', 
            width: expandedDrawerWidth,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            borderRadius: '0 16px 16px 0',
            zIndex: theme.zIndex.drawer + 2,
            position: 'relative',
            touchAction: 'auto',
            WebkitOverflowScrolling: 'touch',
            overflowY: 'auto',
            overflowX: 'hidden'
          },
          '& .MuiListItem-root, & .MuiListItemButton-root, & .MuiListItemIcon-root, & .MuiListItemText-root': {
            position: 'relative',
            zIndex: theme.zIndex.drawer + 3,
            pointerEvents: 'auto',
            touchAction: 'auto',
            userSelect: 'none'
          }
        }}
      >
        {drawer}
      </Drawer>
      
      {/* App Header */}
      <AppBar
        position="fixed"
        sx={{
          width: { 
            xs: '100%',
            sm: `calc(100% - ${drawerWidth}px)`
          },
          ml: { 
            xs: 0,
            sm: `${drawerWidth}px`
          },
          boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
          borderRadius: 0, // Remove rounded corners
          transition: theme.transitions.create(['width', 'margin', 'left'], {
            easing: theme.transitions.easing.easeInOut,
            duration: theme.transitions.duration.standard,
          }),
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Fukuyama Logistics Warehouse Management System
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              mr: 2, 
              p: 1, 
              borderRadius: 0, // Remove rounded corners
              bgcolor: 'rgba(255, 255, 255, 0.15)',
              transition: 'background-color 0.2s',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.25)'
              }
            }}>
              <Avatar 
                sx={{ 
                  width: 32, 
                  height: 32, 
                  mr: 1, 
                  bgcolor: theme.palette.secondary.main,
                  boxShadow: '0 0 0 2px rgba(255,255,255,0.2)',
                }}
              >
                {currentUser?.username?.charAt(0).toUpperCase() || 'U'}
              </Avatar>
              <Typography variant="body2">
                {currentUser?.username}
              </Typography>
            </Box>
            <Button 
              color="inherit" 
              onClick={handleLogout} 
              startIcon={<LogoutIcon />}
              sx={{ 
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: 0, // Remove rounded corners
                px: 2,
                py: 0.7,
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                }
              }}
            >
              Logout
            </Button>
          </Box>
        </Toolbar>
      </AppBar>
      
      {/* Main Content */}
      <Box
        component="main"
        role="main"
        aria-label="Main content"
        sx={{
          flexGrow: 1,
          width: { 
            xs: '100%',
            sm: `calc(100vw - ${drawerWidth}px)`
          },
          minWidth: 0,
          marginLeft: { 
            xs: 0,
            sm: `${drawerWidth}px`
          },
          marginTop: '48px', // Custom AppBar height
          minHeight: 'calc(100vh - 48px)', // Full height minus AppBar
          backgroundColor: theme.palette.grey[50], // Use theme color
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'auto',
          padding: {
            xs: theme.spacing(0.5),
            sm: theme.spacing(0.75),
            md: theme.spacing(2),
          },
          WebkitOverflowScrolling: 'touch',
          scrollBehavior: 'smooth',
          transition: theme.transitions.create(['width', 'margin-left'], {
            easing: theme.transitions.easing.easeInOut,
            duration: theme.transitions.duration.standard,
          }),
          willChange: 'margin-left',
          '&:focus': {
            outline: 'none',
          },
          '&:focus-visible': {
            outline: `2px solid ${theme.palette.primary.main}`,
          },
          fontFamily: theme.typography.fontFamily,
          lineHeight: theme.typography.body1.lineHeight,
          '& > *': {
            width: '100%',
            minWidth: '100%',
            boxSizing: 'border-box',
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column'
          }
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default DashboardLayout; 