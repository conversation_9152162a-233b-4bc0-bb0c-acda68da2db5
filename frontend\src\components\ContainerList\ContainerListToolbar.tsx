import React, { forwardRef } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  IconButton,
  Chip,
  Tooltip,
  Divider
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import DeleteIcon from '@mui/icons-material/Delete';
import GetAppIcon from '@mui/icons-material/GetApp';
import AddIcon from '@mui/icons-material/Add';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import { Container } from '../../types/Container';

interface ContainerListToolbarProps {
  selectedContainers: Container[];
  totalContainers: number;
  onRefresh: () => void;
  onCreateContainer?: () => void;
  onUploadContainer?: () => void;
  onDelete?: (containerNos: string[]) => void;
}

const ContainerListToolbar = forwardRef<HTMLDivElement, ContainerListToolbarProps>(({
  selectedContainers,
  totalContainers,
  onRefresh,
  onCreateContainer,
  onUploadContainer,
  onDelete
}, ref) => {
  const hasSelection = selectedContainers.length > 0;

  const handleBulkDelete = () => {
    if (onDelete && hasSelection) {
      const containerNos = selectedContainers.map(c => c.containerNo);
      onDelete(containerNos);
    }
  };

  return (
    <Box sx={{ 
      borderBottom: 1, 
      borderColor: 'divider',
      bgcolor: 'background.paper'
    }}>
      {/* Main toolbar row */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        px: 2,
        py: 1.5,
        minHeight: '64px'
      }}>
        {/* Left side - Title and stats */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography
            ref={ref}
            variant="h6"
            component="div"
            sx={{ mr: 1 }}
          >
            Container List
          </Typography>
          
          <Chip
            icon={<DirectionsBoatIcon />}
            label={`${totalContainers} containers`}
            size="small"
            variant="outlined"
            sx={{ 
              fontSize: '0.75rem',
              height: '24px'
            }}
          />
          
          {hasSelection && (
            <Chip
              label={`${selectedContainers.length} selected`}
              size="small"
              color="primary"
              sx={{ 
                fontSize: '0.75rem',
                height: '24px'
              }}
            />
          )}
        </Box>

        {/* Right side - Action buttons */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Refresh button */}
          <Tooltip title="Refresh container list">
            <IconButton
              onClick={onRefresh}
              size="small"
              sx={{ 
                color: 'text.secondary',
                '&:hover': { color: 'primary.main' }
              }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* Bulk delete button - only show when containers are selected */}
          {hasSelection && (
            <Tooltip title={`Delete ${selectedContainers.length} selected containers`}>
              <Button
                variant="outlined"
                color="error"
                size="small"
                startIcon={<DeleteIcon />}
                onClick={handleBulkDelete}
                sx={{
                  fontSize: '0.75rem',
                  py: 0.5,
                  px: 1.5
                }}
              >
                Delete ({selectedContainers.length})
              </Button>
            </Tooltip>
          )}

          {/* Upload containers button */}
          {onUploadContainer && (
            <Tooltip title="Upload containers from Excel">
              <Button
                variant="outlined"
                size="small"
                startIcon={<UploadFileIcon />}
                onClick={onUploadContainer}
                sx={{
                  fontSize: '0.75rem',
                  py: 0.5,
                  px: 1.5
                }}
              >
                Upload
              </Button>
            </Tooltip>
          )}

          {/* Create container button */}
          {onCreateContainer && (
            <Tooltip title="Create new container">
              <Button
                variant="contained"
                size="small"
                startIcon={<AddIcon />}
                onClick={onCreateContainer}
                sx={{
                  fontSize: '0.75rem',
                  py: 0.5,
                  px: 1.5
                }}
              >
                New Container
              </Button>
            </Tooltip>
          )}
        </Box>
      </Box>
    </Box>
  );
});

ContainerListToolbar.displayName = 'ContainerListToolbar';

export default ContainerListToolbar;
